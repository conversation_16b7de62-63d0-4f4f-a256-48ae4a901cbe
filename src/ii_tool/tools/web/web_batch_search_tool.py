import json

import httpx
from typing import Any, List, Dict
from ii_tool.tools.base import BaseTool, ToolResult


# Name
NAME = "web_batch_search"
DISPLAY_NAME = "Web Batch Search"

# Tool description
DESCRIPTION = """Performs a web search using a search engine API and returns the top 5 results with each result's title, URL, and snippet. 

When to Use
- Find information on the internet that goes beyond the model's training cutoff
- Research current events, documentation, tutorials, or updates
- Check the latest news and trends

Combine with the web_visit tool to open a result's URL and extract its full content.
"""

# Input schema
INPUT_SCHEMA = {
    "type": "object",
    "properties": {
        "queries": {"type": "array", "items": {"type": "string"}, "description": "The search queries to find information on the web"},
    },
    "required": ["queries"],
}

DEFAULT_TIMEOUT = 120
FAILURE_MESSAGE = "Please try again. If the problem continues, switch to browser tools for manual searching and let the user know that web search is temporarily unavailable."
    
class WebBatchSearchTool(BaseTool):
    name = NAME
    display_name = DISPLAY_NAME
    description = DESCRIPTION
    input_schema = INPUT_SCHEMA
    read_only = True

    def __init__(self, tool_server_url: str, credential: Dict):
        super().__init__()
        self.tool_server_url = tool_server_url
        self.credential = credential
        
    async def execute(
        self,
        tool_input: dict[str, Any],
    ) -> ToolResult:
        queries = tool_input["queries"]
        
        try:
            async with httpx.AsyncClient(timeout=DEFAULT_TIMEOUT) as client:
                response = await client.post(
                    f"{self.tool_server_url}/v2/web-search",
                    json={"queries": queries, "session_id": self.credential['session_id']},
                    headers={
                        "Authorization": f"Bearer {self.credential['user_api_key']}",
                    },
                    timeout=DEFAULT_TIMEOUT,
                )
                response.raise_for_status()
            
        except httpx.TimeoutException:
            return ToolResult(
                llm_content=f"The search engine is taking too long to respond. It might be overloaded. {FAILURE_MESSAGE}",
                is_error=True,
            )
        except httpx.NetworkError:
            return ToolResult(
                llm_content=f"The search engine is unreachable (network issue). {FAILURE_MESSAGE}",
                is_error=True,
            )
        except httpx.HTTPStatusError as e:
            return ToolResult(
                llm_content=f"The search request failed: {e}. {FAILURE_MESSAGE}",
                is_error=True,
            )

        response_data = response.json()
        results = response_data.get("results", [])
        result_str = ""
        for i, query in enumerate(queries):
            result_str += f"Query: {query}\n"
            for j, result in enumerate(results[i]):
                result_str += f"Output {j+1}:\n"
                result_str += f"Title: {result['title']}\n"
                result_str += f"URL: {result['url']}\n"
                result_str += f"Snippet: {result['content']}\n"
                result_str += "-----------------------------------\n"


        if len(results) == 0:
            return ToolResult(
                llm_content=f"The search engine processed your query '{queries}' successfully but found no matching results. Try rephrasing with different keywords, broader terms, or check for typos.",
                user_display_content="", # NOTE: to compatible with the current frontend implementation
                is_error=False,
            )
        
        user_display_results =[]
        for result in results:
            user_display_results.extend(result)
        
        return ToolResult(
            llm_content=result_str,
            user_display_content=json.dumps(user_display_results, indent=2),
            is_error=False,
        )

    async def execute_mcp_wrapper(self, queries: List[str]):
        return await self._mcp_wrapper(
            tool_input={
                "queries": queries,
            }
        )