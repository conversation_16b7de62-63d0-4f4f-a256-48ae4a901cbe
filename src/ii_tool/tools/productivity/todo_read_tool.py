"""TodoRead tool for reading the current session's task list."""

from typing import Any
from ii_tool.tools.base import BaseTool, ToolResult
from ii_tool.tools.productivity.shared_state import get_todo_manager


# Constants
EMPTY_MESSAGE = "No todos found"
SUCCESS_MESSAGE = "Remember to continue to use update and read from the todo list as you make progress. Here is the current list: {todos}"

# Name
NAME = "TodoRead"
DISPLAY_NAME = "Read todo list"

# Tool description
DESCRIPTION = """Use this tool to read the current to-do list for the session. This tool should be used proactively and frequently to ensure that you are aware of
the status of the current task list. You should make use of this tool as often as possible, especially in the following situations:
- At the beginning of conversations to see what's pending
- Before starting new tasks to prioritize work
- When the user asks about previous tasks or plans
- Whenever you're uncertain about what to do next
- After completing tasks to update your understanding of remaining work
- After every few messages to ensure you're on track

Usage:
- This tool takes in no parameters. So leave the input blank or empty. DO NOT include a dummy object, placeholder string or a key like \"input\" or \"empty\". LEAVE IT BLANK.
- Returns a list of todo items with their status, priority, and content
- Use this information to track progress and plan next steps
- If no todos exist yet, an empty list will be returned"""

# Input schema
INPUT_SCHEMA = {
    "type": "object",
    "properties": {},
    "required": []
}


class TodoReadTool(BaseTool):
    """Tool for reading the current to-do list for the session."""
    
    name = NAME
    display_name = DISPLAY_NAME
    description = DESCRIPTION
    input_schema = INPUT_SCHEMA
    read_only = True

    async def execute(
        self,
        tool_input: dict[str, Any],
    ) -> ToolResult:
        """Read and return the current todo list."""
        manager = get_todo_manager()
        todos = manager.get_todos()
        
        if not todos:
            return ToolResult(
                llm_content=EMPTY_MESSAGE,
                is_error=False
            )
        
        return ToolResult(
            llm_content=SUCCESS_MESSAGE.format(todos=todos),
            is_error=False
        )

    async def execute_mcp_wrapper(self):
        return await self._mcp_wrapper(
            tool_input={}
        )