from typing import Optional, Literal, List

from ..logger import get_logger
from .base import Base<PERSON>ebVisitClient
from .firecrawl import FireCrawlWebVisitClient
from .gemini import GeminiWebVisitClient
from .jina import JinaWebVisitClient
from .tavily import TavilyWebVisitClient
from .beautifulsoup import BeautifulSoupWebVisitClient
from .config import WebVisitConfig, CompressorConfig

logger = get_logger("ii_tool.web_visit.factory")

def create_web_visit_client(settings: WebVisitConfig, compressor_config: CompressorConfig, client_type: Optional[Literal["firecrawl", "gemini", "jina", "tavily", "beautifulsoup"]] = None) -> BaseWebVisitClient:
    """
    Factory function that creates a web visit client based on available API keys.
    Priority order: FireCrawl > Gemini > Jina > Tavily > BeautifulSoup

    Args:
        settings: Settings object containing API keys
        compressor_config: Compressor configuration
        client_type: Specific client type to use (optional)

    Returns:
        BaseWebVisitClient: An instance of a web visit client
    """
    firecrawl_key = settings.firecrawl_api_key
    gemini_key = settings.gemini_api_key
    jina_key = settings.jina_api_key
    tavily_key = settings.tavily_api_key

    if client_type == "firecrawl":
        if not firecrawl_key:
            raise ValueError("FireCrawl API key not found")
        logger.info("Using FireCrawl Client")
        return FireCrawlWebVisitClient(api_key=firecrawl_key, compressor_config=compressor_config)

    if client_type == "gemini":
        if not gemini_key:
            raise ValueError("Gemini API key not found")
        print("Using Gemini Client")
        return GeminiWebVisitClient(api_key=gemini_key)

    if client_type == "jina":
        if not jina_key:
            raise ValueError("Jina API key not found")
        logger.info("Using Jina Client")
        return JinaWebVisitClient(api_key=jina_key, compressor_config=compressor_config)

    if client_type == "tavily":
        if not tavily_key:
            raise ValueError("Tavily API key not found")
        logger.info("Using Tavily Client")
        return TavilyWebVisitClient(api_key=tavily_key, compressor_config=compressor_config)

    if client_type == "beautifulsoup":
        logger.info("Using Soup Client")
        return BeautifulSoupWebVisitClient(compressor_config=compressor_config)

    # Default priority order if no client_type specified
    if firecrawl_key:
        logger.info("Using FireCrawl to visit webpage")
        return FireCrawlWebVisitClient(api_key=firecrawl_key, compressor_config=compressor_config)

    if gemini_key:
        logger.info("Using Gemini to visit webpage")
        return GeminiWebVisitClient(api_key=gemini_key)

    if jina_key:
        logger.info("Using Jina to visit webpage")
        return JinaWebVisitClient(api_key=jina_key, compressor_config=compressor_config)

    if tavily_key:
        logger.info("Using Tavily to visit webpage")
        return TavilyWebVisitClient(api_key=tavily_key, compressor_config=compressor_config)

    # Fall back to BeautifulSoup if no API keys are available
    logger.info("Using BeautifulSoup to visit webpage (no API keys found)")
    return BeautifulSoupWebVisitClient(compressor_config=compressor_config)


def create_all_web_visit_clients(settings: WebVisitConfig, compressor_config: CompressorConfig) -> List[BaseWebVisitClient]:
    """
    Creates a list of all available web visit clients in fallback order.
    Priority order: FireCrawl > Jina > Tavily > BeautifulSoup

    Args:
        settings: Settings object containing API keys
        compressor_config: Compressor configuration

    Returns:
        List[BaseWebVisitClient]: List of available web visit clients
    """
    clients = []

    # Add clients in priority order: FireCrawl > Jina > Tavily > BeautifulSoup
    firecrawl_key = settings.firecrawl_api_key
    jina_key = settings.jina_api_key
    tavily_key = settings.tavily_api_key
    gemini_key = settings.gemini_api_key

    if firecrawl_key:
        try:
            clients.append(FireCrawlWebVisitClient(api_key=firecrawl_key, compressor_config=compressor_config))
        except Exception:
            pass

    if jina_key:
        try:
            clients.append(JinaWebVisitClient(api_key=jina_key, compressor_config=compressor_config))
        except Exception:
            pass

    if tavily_key:
        try:
            clients.append(TavilyWebVisitClient(api_key=tavily_key, compressor_config=compressor_config))
        except Exception:
            pass

    # Always add BeautifulSoup as the last fallback
    clients.append(BeautifulSoupWebVisitClient(compressor_config=compressor_config))

    return clients