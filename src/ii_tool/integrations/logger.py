import logging
import sys
from typing import Optional

def get_logger(
    name: Optional[str] = None,
    level: int = logging.INFO,
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
) -> logging.Logger:
    """
    Get or create a configured logger instance.

    Args:
        name: Logger name. Defaults to "ii_tool".
        level: Logging level. Defaults to INFO.
        format: Log message format string.

    Returns:
        Configured logger instance.
    """
    logger_name = name or "ii_tool"
    logger = logging.getLogger(logger_name)

    # Only configure if no handlers exist to avoid duplicate configuration
    if not logger.handlers:
        handler = logging.StreamHandler(sys.stdout)
        handler.setFormatter(logging.Formatter(format))
        logger.addHandler(handler)
        logger.setLevel(level)
        logger.propagate = False

    return logger