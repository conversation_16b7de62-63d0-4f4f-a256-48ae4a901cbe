"""Storage location utilities."""


def get_conversation_agent_state_path(session_id: str) -> str:
    """Returns path to state file (core conversation data only)"""
    return f"sessions/{session_id}/agent_state.json"


def get_conversation_metadata_path(session_id: str) -> str:
    """Returns path to metadata file"""
    return f"sessions/{session_id}/metadata.json"


def get_session_file_path(session_id: str, file_id: str, file_name: str) -> str:
    """Returns path to file created in session (e.g. image/video generated by agent)"""
    return f"sessions/{session_id}/files/{file_id}-{file_name}"


def get_user_upload_file_path(user_id: str, file_id: str, file_name: str) -> str:
    """Returns path to user file upload"""
    return f"users/{user_id}/uploads/{file_id}-{file_name}"


def get_user_avatar_path(user_id: str, extension: str) -> str:
    """Returns path to user avatar"""
    return f"users/{user_id}/avatar.{extension}"