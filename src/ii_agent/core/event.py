from pydantic import BaseModel, Field
from typing import Any, Optional
import enum
import time


class AgentStatus(str, enum.Enum):
    READY = "ready"
    RUNNING = "running"
    CANCELLED = "cancelled"


class EventType(str, enum.Enum):
    CONNECTION_ESTABLISHED = "connection_established"
    STATUS_UPDATE = "status_update"
    AGENT_INITIALIZED = "agent_initialized"
    WORKSPACE_INFO = "workspace_info"
    PROCESSING = "processing"
    AGENT_THINKING = "agent_thinking"
    TOOL_CALL = "tool_call"
    TOOL_RESULT = "tool_result"
    AGENT_RESPONSE = "agent_response"
    AGENT_RESPONSE_INTERRUPTED = "agent_response_interrupted"
    STREAM_COMPLETE = "stream_complete"
    ERROR = "error"
    SYSTEM = "system"
    PONG = "pong"
    UPLOAD_SUCCESS = "upload_success"
    BROWSER_USE = "browser_use"
    FILE_EDIT = "file_edit"
    USER_MESSAGE = "user_message"
    PROMPT_GENERATED = "prompt_generated"
    TOOL_CONFIRMATION = "tool_confirmation"
    SANDBOX_STATUS = "sandbox_status"
    COMPLETE = "complete"
    SUB_AGENT_COMPLETE = "sub_agent_complete"
    METRICS_UPDATE = "metrics_update"
    MODEL_COMPACT = "model_compact"


class RealtimeEvent(BaseModel):
    type: EventType
    content: dict[str, Any]
    timestamp: Optional[float] = Field(default_factory=time.time)
