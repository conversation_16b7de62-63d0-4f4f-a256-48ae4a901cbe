"""add bonus_credits to users table

Revision ID: d3561654b919
Revises: ea3f7c9d1254
Create Date: 2025-09-27 13:16:48.096293

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "d3561654b919"
down_revision: Union[str, None] = "ea3f7c9d1254"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # Add bonus_credits column to users table
    op.add_column(
        "users",
        sa.Column("bonus_credits", sa.Float(), nullable=False, server_default="0.0"),
    )

    op.alter_column(
        "billing_transactions",
        "created_at",
        existing_type=sa.TIMESTAMP(),
        nullable=True,
        existing_server_default=sa.text("(CURRENT_TIMESTAMP)"),
    )
    op.alter_column(
        "billing_transactions",
        "updated_at",
        existing_type=sa.TIMESTAMP(),
        nullable=True,
        existing_server_default=sa.text("(CURRENT_TIMESTAMP)"),
    )
    op.drop_index(
        op.f("ix_billing_transactions_subscription_id"),
        table_name="billing_transactions",
    )
    op.drop_index(
        op.f("ix_billing_transactions_user_id"), table_name="billing_transactions"
    )
    op.create_index(
        "idx_billing_transactions_subscription",
        "billing_transactions",
        ["stripe_subscription_id"],
        unique=False,
    )
    op.create_index(
        "idx_billing_transactions_user_id",
        "billing_transactions",
        ["user_id"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # Remove bonus_credits column from users table
    op.drop_column("users", "bonus_credits")

    op.drop_index("idx_billing_transactions_user_id", table_name="billing_transactions")
    op.drop_index(
        "idx_billing_transactions_subscription", table_name="billing_transactions"
    )
    op.create_index(
        op.f("ix_billing_transactions_user_id"),
        "billing_transactions",
        ["user_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_billing_transactions_subscription_id"),
        "billing_transactions",
        ["stripe_subscription_id"],
        unique=False,
    )
    op.alter_column(
        "billing_transactions",
        "updated_at",
        existing_type=sa.TIMESTAMP(),
        nullable=False,
        existing_server_default=sa.text("(CURRENT_TIMESTAMP)"),
    )
    op.alter_column(
        "billing_transactions",
        "created_at",
        existing_type=sa.TIMESTAMP(),
        nullable=False,
        existing_server_default=sa.text("(CURRENT_TIMESTAMP)"),
    )
    # ### end Alembic commands ###
