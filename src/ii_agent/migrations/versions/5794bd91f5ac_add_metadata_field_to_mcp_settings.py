"""add_metadata_field_to_mcp_settings

Revision ID: 5794bd91f5ac
Revises: ff19b82b7805
Create Date: 2025-09-15 21:58:29.591294

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
from sqlalchemy.dialects import sqlite

# revision identifiers, used by Alembic.
revision: str = '5794bd91f5ac'
down_revision: Union[str, None] = 'ff19b82b7805'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('mcp_settings', sa.Column('metadata', sqlite.JSON().with_variant(postgresql.JSONB(), 'postgresql'), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('mcp_settings', 'metadata')
    # ### end Alembic commands ###
