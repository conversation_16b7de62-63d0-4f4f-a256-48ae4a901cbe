"""generate_api_keys_for_existing_users

Revision ID: 5748a2cbf33f
Revises: c4dcd998fe12
Create Date: 2025-09-16 09:52:05.541581

"""
from typing import Sequence, Union
import uuid
import secrets
import string
from datetime import datetime, timezone

from alembic import op
import sqlalchemy as sa
from sqlalchemy import text


# revision identifiers, used by Alembic.
revision: str = '5748a2cbf33f'
down_revision: Union[str, None] = 'c4dcd998fe12'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def generate_api_key(length: int = 32) -> str:
    """Generate a secure random API key."""
    alphabet = string.ascii_letters + string.digits
    return ''.join(secrets.choice(alphabet) for _ in range(length))


def generate_prefixed_api_key(prefix: str = "ii", length: int = 32) -> str:
    """Generate a prefixed API key for easy identification."""
    remaining_length = max(8, length - len(prefix) - 1)
    random_part = generate_api_key(remaining_length)
    return f"{prefix}_{random_part}"


def upgrade() -> None:
    """Generate API keys for existing users who don't have them."""
    connection = op.get_bind()
    
    # Find users who don't have API keys yet
    # Using raw SQL to avoid model import issues in migrations
    users_without_keys = connection.execute(text("""
        SELECT u.id, u.email 
        FROM users u 
        LEFT JOIN api_keys ak ON u.id = ak.user_id 
        WHERE ak.user_id IS NULL
    """)).fetchall()
    
    if not users_without_keys:
        print("No existing users need API keys generated.")
        return
    
    print(f"Generating API keys for {len(users_without_keys)} existing users...")
    
    # Generate API keys for each user
    for user in users_without_keys:
        api_key_value = generate_prefixed_api_key()
        api_key_id = str(uuid.uuid4())
        created_at = datetime.now(timezone.utc)
        
        connection.execute(text("""
            INSERT INTO api_keys (id, user_id, api_key, is_active, created_at, updated_at)
            VALUES (:id, :user_id, :api_key, :is_active, :created_at, :updated_at)
        """), {
            'id': api_key_id,
            'user_id': user.id,
            'api_key': api_key_value,
            'is_active': True,
            'created_at': created_at,
            'updated_at': created_at
        })
        
        print(f"Generated API key for user: {user.email}")
    
    print(f"Successfully generated API keys for {len(users_without_keys)} users.")


def downgrade() -> None:
    """Downgrade operation - remove API keys that were generated by this migration.
    
    Note: This is a data migration downgrade. We cannot safely identify which
    API keys were generated by this specific migration vs. those created naturally
    through user registration. Therefore, this downgrade is a no-op.
    
    If you need to remove API keys, consider doing it manually with appropriate
    WHERE clauses to target specific keys.
    """
    print("Downgrade: No action taken. API keys generated by this migration remain in place.")
    print("Manual removal required if needed.")
