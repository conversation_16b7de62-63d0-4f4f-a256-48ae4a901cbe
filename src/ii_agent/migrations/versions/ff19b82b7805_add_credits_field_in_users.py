"""add: credits field in users

Revision ID: ff19b82b7805
Revises: 8b8b9741a434
Create Date: 2025-09-11 14:29:34.237544

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "ff19b82b7805"
down_revision: Union[str, None] = "8b8b9741a434"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # Note: Default value here is for existing users during migration.
    # New users will get their default from config.default_user_credits
    op.add_column(
        "users",
        sa.Column("credits", sa.Float(), nullable=False, server_default="1000.0"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("users", "credits")
    # ### end Alembic commands ###
