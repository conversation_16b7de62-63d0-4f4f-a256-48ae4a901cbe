from typing import Any, List, Optional
from ii_tool.tools.base import BaseTool
from ii_tool.core import Workspace<PERSON>anager
from ii_agent.core.event_stream import EventStream
from ii_agent.llm.context_manager.base import ContextManager
from ii_agent.controller.agent import Agent
from ii_agent.controller.agent_controller import <PERSON><PERSON><PERSON>roller
from ii_agent.controller.tool_manager import Agent<PERSON><PERSON><PERSON>anager
from ii_agent.controller.state import State


class BaseAgentTool(BaseTool):
    name: str
    display_name: str
    description: str
    input_schema: dict[str, Any]
    read_only: bool

    def __init__(
        self,
        agent: Agent,
        tools: List[BaseTool],
        context_manager: ContextManager,
        event_stream: EventStream,
        max_turns: int = 200,
        config: Optional[Any] = None,
    ):
        self.agent = agent
        self.tools = tools
        self.context_manager = context_manager
        self.event_stream = event_stream
        self.max_turns = max_turns
        self.config = config
        self._setup_agent_controller()
    
    def cancel(self):
        self.controller.cancel()
    
    def _setup_agent_controller(self):
        tool_manager = AgentToolManager()
        tool_manager.register_tools(self.tools)

        self.controller =  AgentController(
            agent=self.agent,
            tool_manager=tool_manager,
            init_history=State(),
            event_stream=self.event_stream,
            context_manager=self.context_manager,
            max_turns=self.max_turns,
            interactive_mode=False, # Not supported for agent as tools
            config=self.config,
            is_sub_agent=True
        )