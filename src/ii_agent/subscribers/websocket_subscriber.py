import asyncio
import logging
from typing import List
from fastapi import WebSocket
from fastapi.websockets import WebSocketState

from ii_agent.core.event import Realtime<PERSON><PERSON>, EventType


class WebSocketSubscriber:
    """Subscriber that handles WebSocket communication for events."""

    def __init__(self, websockets: List[WebSocket], logger: logging.Logger = None):
        self.websockets = websockets
        self._logger = logger or logging.getLogger(__name__)

    async def handle_event(self, event: RealtimeEvent) -> None:
        """Handle an event by broadcasting it to all WebSockets if appropriate."""
        # Only send to websockets if this is not an event from the client
        if self.websockets:
            event_data = event.model_dump()
            failed_websockets = []
            
            for websocket in self.websockets:
                try:
                    await websocket.send_json(event_data)
                except Exception as e:
                    # If websocket send fails, just log it and mark for removal
                    self._logger.warning(
                        f"Failed to send message to websocket: {str(e)}"
                    )
                    failed_websockets.append(websocket)
            
            # Remove failed websockets
            for ws in failed_websockets:
                if ws in self.websockets:
                    self.websockets.remove(ws)

    def add_websocket(self, websocket: WebSocket) -> None:
        """Add a websocket to the list."""
        if websocket not in self.websockets:
            self.websockets.append(websocket)
    
    def remove_websocket(self, websocket: WebSocket) -> None:
        """Remove a websocket from the list."""
        if websocket in self.websockets:
            self.websockets.remove(websocket)