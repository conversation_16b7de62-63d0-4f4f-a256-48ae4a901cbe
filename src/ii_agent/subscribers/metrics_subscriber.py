"""Subscriber that tracks LLM metrics per session."""

import logging
from typing import Optional
from datetime import datetime, timezone

from ii_agent.core.event import RealtimeEvent, EventType
from ii_agent.db.manager import get_db
from ii_agent.metrics.models import LLMMetrics, TokenUsage, ToolUsage
from ii_agent.metrics.service import accumulate_session_metrics, is_user_provided_model
from ii_agent.server.credits.service import deduct_user_credits
from ii_agent.db.models import Session
from sqlalchemy import select
from ii_tool.tools.media.image_generate import ImageGenerateTool
from ii_tool.tools.media.video_generate import VideoGenerateTool

logger = logging.getLogger(__name__)


class MetricsSubscriber:
    """Subscriber that handles metrics updates for sessions."""

    def __init__(self, session_id: Optional[str]):
        self.session_id = session_id
        self.is_user_model: Optional[bool] = (
            None  # Will be set during async initialization
        )

    async def _query_model_source(self, session_id: str) -> bool:
        """Query database once to determine if session uses user-provided model."""
        async with get_db() as db_session:
            return await is_user_provided_model(
                db_session=db_session, session_id=session_id
            )

    async def _get_user_id_from_session(self, session_id: str) -> Optional[str]:
        """Get user ID for the given session."""
        try:
            async with get_db() as db_session:
                result = await db_session.execute(
                    select(Session.user_id).where(Session.id == session_id)
                )
                user_id = result.scalar_one_or_none()
                return user_id
        except Exception as e:
            logger.error(
                f"Error getting user ID for session {session_id}: {e}", exc_info=True
            )
            return None

    async def handle_event(self, event: RealtimeEvent) -> None:
        """Handle an event, specifically looking for METRICS_UPDATE and TOOL_RESULT events."""

        # Skip if we don't have a session to track
        if not self.session_id:
            return

        try:
            if event.type == EventType.METRICS_UPDATE:
                await self._handle_metrics_update(event)
            elif event.type == EventType.TOOL_RESULT:
                await self._handle_tool_result(event)

        except Exception as e:
            logger.error(f"Error processing event {event.type}: {e}", exc_info=True)

    async def _handle_metrics_update(self, event: RealtimeEvent) -> None:
        """Handle METRICS_UPDATE events for LLM token usage."""
        # Lazy initialization - check if this is a user-provided model (only once)
        if self.is_user_model is None:
            if self.session_id:
                self.is_user_model = await self._query_model_source(self.session_id)
            else:
                self.is_user_model = False  # Default to system model if no session

        # Skip charging for user-provided models
        if self.is_user_model:
            logger.info(
                f"Skipping LLM credits for user-provided model in session {self.session_id}"
            )
            return

        # Extract metrics data from event
        content = event.content

        token_usage = TokenUsage(**content)

        metrics = LLMMetrics(
            token_usage=token_usage,
            timestamp=datetime.now(timezone.utc),
            session_id=self.session_id,
        )

        # Log metrics
        logger.info(
            f"Metrics updated for session {self.session_id}: "
            f"prompt_tokens={metrics.token_usage.prompt_tokens}, "
            f"completion_tokens={metrics.token_usage.completion_tokens}, "
            f"cache_read_tokens={metrics.token_usage.cache_read_tokens}, "
            f"cache_write_tokens={metrics.token_usage.cache_write_tokens}, "
            f"credits={metrics.calculate_credits()}, "
            f"model={metrics.token_usage.model_name}"
        )

        # Update database immediately for each event
        async with get_db() as db_session:
            await accumulate_session_metrics(
                db_session=db_session,
                session_id=self.session_id,
                credits=-(metrics.credits if metrics.credits else 0.0),
            )

            # Deduct credits from user's balance for system-provided models
            credits_to_deduct = metrics.credits if metrics.credits else 0.0
            if credits_to_deduct > 0:
                user_id = await self._get_user_id_from_session(self.session_id)
                if user_id:
                    success = await deduct_user_credits(
                        db_session=db_session,
                        user_id=user_id,
                        amount=credits_to_deduct,
                        description=f"LLM usage for model {metrics.token_usage.model_name}",
                    )
                    if success:
                        logger.info(
                            f"Deducted {credits_to_deduct} credits from user {user_id}"
                        )
                    else:
                        logger.warning(
                            f"Failed to deduct {credits_to_deduct} credits from user {user_id}"
                        )

    async def _handle_tool_result(self, event: RealtimeEvent) -> None:
        """Handle TOOL_RESULT events for tool usage tracking."""
        pass

    def update_session_id(self, session_id: Optional[str]) -> None:
        """Update the session ID."""
        self.session_id = session_id
