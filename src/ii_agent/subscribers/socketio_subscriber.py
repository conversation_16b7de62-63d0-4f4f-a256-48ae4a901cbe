import logging

import socketio

from ii_agent.core.event import RealtimeEvent

logger = logging.getLogger(__name__)


class SocketIOSubscriber:
    """Subscriber that broadcasts events to a Socket.IO room."""

    def __init__(self, sio: socketio.AsyncServer, room: str):
        """
        Initialize the SocketIO subscriber.

        Args:
            sio: The Socket.IO server instance
            room: The room name to broadcast to
        """
        self.sio = sio
        self.room = room

    async def handle_event(self, event: RealtimeEvent) -> None:
        """
        Handle a realtime event by broadcasting it to the Socket.IO room.

        Args:
            event: The realtime event to broadcast
        """
        try:
            # Convert event to dict for JSON serialization
            event_data = {
                "type": event.type,
                "content": event.content,
            }

            # Broadcast to all clients in the room
            logger.debug(f"Broadcast event {event.content} to room {self.room}")
            room_size = self.sio.manager.get_participants("/", self.room)
            logger.debug(f"Room size: {len(list(room_size))}")
            await self.sio.emit("chat_event", event_data, room=self.room)

        except Exception as e:
            logger.error(f"Error broadcasting event to room {self.room}: {e}")
