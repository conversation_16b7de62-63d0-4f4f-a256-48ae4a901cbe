import asyncio
import logging
import uuid
from typing import Any, Dict, Optional

import socketio

from ii_agent.server.services.message_service import MessageService
from ii_agent.server.services.sandbox_service import SandboxService
from ii_agent.subscribers.socketio_subscriber import SocketIOSubscriber
from ii_agent.subscribers.database_subscriber import DatabaseSubscriber
from ii_agent.subscribers.metrics_subscriber import MetricsSubscriber
from ii_agent.core.config.ii_agent_config import IIAgentConfig
from ii_agent.server.services.session_service import SessionService
from ii_agent.server.websocket.chat_session import ChatSession

logger = logging.getLogger(__name__)


class ConnectionManager:
    """Manages Socket.IO connections and their associated chat sessions."""

    def __init__(
        self,
        message_service: MessageService,
        session_service: SessionService,
        sandbox_service: SandboxService,
        config: IIAgentConfig,
    ):
        # Active chat sessions mapped by session UUID
        self.sessions: Dict[uuid.UUID, ChatSession] = {}
        self.session_service = session_service
        self.message_service = message_service
        self.sandbox_service = sandbox_service

        self.config = config
        # Track which socket IDs belong to which sessions
        self.socket_to_session: Dict[str, uuid.UUID] = {}
        self.sio: Optional[socketio.AsyncServer] = None
        # Track sessions being cleaned up to prevent race conditions
        self.session_locks: Dict[uuid.UUID, asyncio.Lock] = {}
        # Track cleanup tasks to prevent race conditions during page refresh
        self.cleanup_tasks: Dict[uuid.UUID, asyncio.Task] = {}

    def set_sio(self, sio: socketio.AsyncServer):
        self.sio = sio

    async def handle_message(self, sid: str, data: Dict[str, Any]):
        session_uuid_str = data.get("session_uuid")
        logger.info(f"Socket to session: {self.socket_to_session}")
        session = await self.get_session(sid, session_uuid_str)
        if not session:
            raise RuntimeError("Session not found")
        session_uuid = session.session_uuid
        async with self.session_locks[session_uuid]:
            if session_uuid not in self.sessions:
                logger.info("Session is cleaned up")
                return
            await self.message_service.process_message(
                data.get("type", ""),
                data.get("content", {}),
                session,
            )

    async def connect(
        self,
        sid: str,
        session_uuid_str: Optional[str] = None,
        user_id: Optional[str] = None,
    ) -> ChatSession:
        """Accept a new Socket.IO connection and create or join a chat session.

        Args:
            sid: Socket.IO session ID
            session_uuid_str: Optional session UUID string
            user_id: Optional authenticated user ID

        Returns:
            ChatSession instance
        """
        if not self.sio:
            raise RuntimeError("Socket.IO server not initialized")
        # Get session UUID from params or generate new one
        if session_uuid_str:
            session_uuid = uuid.UUID(session_uuid_str)
        else:
            session_uuid = uuid.uuid4()

        # Track socket to session mapping
        self.socket_to_session[sid] = session_uuid

        # Join the Socket.IO room for this session
        try:
            await self.sio.enter_room(sid, str(session_uuid))
            logger.info(f"Socket {sid} joined room {session_uuid}")
        except ValueError as e:
            logger.error(f"Failed to join room {session_uuid} for socket {sid}: {e}")
            # Clean up the socket mapping since joining failed
            if sid in self.socket_to_session:
                del self.socket_to_session[sid]
            raise

        # Check if session already exists
        if session_uuid not in self.session_locks:
            self.session_locks[session_uuid] = asyncio.Lock()

        async with self.session_locks[session_uuid]:
            # Cancel any pending cleanup task for this session
            if session_uuid in self.cleanup_tasks:
                cleanup_task = self.cleanup_tasks[session_uuid]
                if not cleanup_task.done():
                    cleanup_task.cancel()
                    logger.info(f"Cancelled pending cleanup for session {session_uuid}")
                del self.cleanup_tasks[session_uuid]

            if session_uuid in self.sessions:
                # Add socket to existing session
                session = self.sessions[session_uuid]
                session.add_socket(sid)
                logger.info(
                    f"Socket.IO client joined existing session {session_uuid} for user {user_id}: {sid}"
                )
            else:
                # Create a new chat session using the session service
                if not self.session_service:
                    raise RuntimeError("SessionService not initialized")

                session = await self.session_service.create_session(
                    sockets=[sid],
                    session_uuid=session_uuid,
                    user_id=user_id,
                    sio=self.sio,
                )
                socketio_subscriber = SocketIOSubscriber(self.sio, str(session_uuid))
                database_subscriber = DatabaseSubscriber(session_uuid)
                metrics_subscriber = MetricsSubscriber(str(session_uuid))
                session.setup_subscribers(
                    [
                        socketio_subscriber.handle_event,
                        database_subscriber.handle_event,
                        metrics_subscriber.handle_event,
                    ]
                )

                # Note: Slide content hook will be registered after sandbox is initialized in message_service

                self.sessions[session_uuid] = session

                logger.info(
                    f"New chat session {session_uuid} created for user {user_id}: {sid}"
                )
            return session

    async def disconnect(self, sid: str):
        """Handle Socket.IO disconnection and cleanup."""
        logger.info(f"Socket.IO client disconnecting: {sid}")
        logger.info(f"Session uuid: {self.socket_to_session.get(sid)}")
        logger.info(f"Socket to session mapping: {self.socket_to_session}")
        logger.info(f"Sessions: {self.sessions}")

        if not self.sio:
            raise RuntimeError("Socket.IO server not initialized")

        if sid not in self.socket_to_session:
            logger.warning(
                f"Socket.IO client disconnecting without session mapping: {sid}"
            )
            return

        session_uuid = self.socket_to_session[sid]
        
        # Leave the Socket.IO room immediately (non-blocking)
        try:
            await self.sio.leave_room(sid, str(session_uuid))
        except ValueError as e:
            logger.warning(
                f"Failed to leave room {session_uuid} for socket {sid}: {e}"
            )
            # Continue with cleanup even if leaving room fails

        # Remove socket from mapping immediately (non-blocking operation)
        del self.socket_to_session[sid]

        # Schedule socket removal and cleanup check without holding the lock
        asyncio.create_task(self._remove_socket_and_check_cleanup(session_uuid, sid))

    async def _remove_socket_and_check_cleanup(self, session_uuid: uuid.UUID, sid: str):
        """Remove socket from session and check if cleanup is needed."""
        try:
            if session_uuid not in self.session_locks:
                self.session_locks[session_uuid] = asyncio.Lock()

            async with self.session_locks[session_uuid]:
                if session_uuid in self.sessions:
                    session = self.sessions[session_uuid]
                    session.remove_socket(sid)
                    logger.info(f"Removed socket {sid} from session {session_uuid}")

                    # Check if there are any other sockets that joined while we were processing
                    # This prevents race condition where new socket joins during disconnect
                    current_mapped_sockets = [
                        s for s, sess_id in self.socket_to_session.items() 
                        if sess_id == session_uuid
                    ]
                    
                    # Update session sockets to include any newly connected ones
                    for mapped_sid in current_mapped_sockets:
                        if mapped_sid not in session.sockets:
                            session.add_socket(mapped_sid)
                            logger.info(f"Re-added socket {mapped_sid} to session {session_uuid}")

                    # Only schedule cleanup if truly no sockets remain
                    if not session.sockets:
                        if session_uuid not in self.cleanup_tasks:
                            logger.info(f"Scheduling cleanup for session {session_uuid}")
                            cleanup_task = asyncio.create_task(
                                self._delayed_cleanup_session(session_uuid)
                            )
                            self.cleanup_tasks[session_uuid] = cleanup_task
                            logger.info(
                                f"Scheduled delayed cleanup for session {session_uuid}"
                            )
                        else:
                            logger.info(
                                f"Cleanup already scheduled for session {session_uuid}"
                            )
                    else:
                        logger.info(
                            f"Session {session_uuid} still has {len(session.sockets)} socket(s), no cleanup needed"
                        )
        except Exception as e:
            logger.error(f"Error removing socket {sid} from session {session_uuid}: {e}")

    async def _delayed_cleanup_session(
        self, session_uuid: uuid.UUID, delay_seconds: float = 60.0
    ):
        """Perform delayed cleanup of a session to handle page refresh race conditions.
        
        Background tasks continue running even when no websockets are connected.
        Only sandbox and session resources are cleaned up after the task completes.
        """
        try:
            # Wait for any active task to finish before starting cleanup delay
            # This ensures background tasks continue running even when no websockets are connected
            if session_uuid in self.sessions:
                session = self.sessions[session_uuid]
                if session.has_active_task():
                    logger.info(
                        f"Background task still running for session {session_uuid}, waiting for completion before cleanup"
                    )
                    try:
                        # Shield the active task from cancellation to ensure it completes
                        await asyncio.shield(session.active_task)
                        logger.info(
                            f"Background task completed for session {session_uuid}"
                        )
                    except asyncio.CancelledError:
                        # If we're being cancelled, don't propagate to the active task
                        logger.info(
                            f"Cleanup task cancelled while waiting for active task in session {session_uuid}"
                        )
                        logger.info(
                            f"Background task will continue running for session {session_uuid}"
                        )
                        raise

            await asyncio.sleep(delay_seconds)

            if session_uuid not in self.session_locks:
                self.session_locks[session_uuid] = asyncio.Lock()

            async with self.session_locks[session_uuid]:
                # Check if session still exists and has no sockets
                if session_uuid in self.sessions:
                    session = self.sessions[session_uuid]
                    
                    # Double-check socket mapping for any new connections
                    current_mapped_sockets = [
                        s for s, sess_id in self.socket_to_session.items() 
                        if sess_id == session_uuid
                    ]
                    
                    # Re-add any sockets that joined during cleanup delay
                    for mapped_sid in current_mapped_sockets:
                        if mapped_sid not in session.sockets:
                            session.add_socket(mapped_sid)
                            logger.info(f"Re-added socket {mapped_sid} to session {session_uuid} during cleanup check")
                    
                    if not session.sockets:
                        # Only cleanup if no active task and no sockets
                        if not session.has_active_task():
                            # Clean up shared resources
                            await self.sandbox_service.cleanup_sandbox_for_session(
                                session_uuid
                            )
                            await session.cleanup()
                            del self.sessions[session_uuid]
                            logger.info(
                                f"Session {session_uuid} cleaned up after delay - no connections and no active task"
                            )
                        else:
                            logger.info(
                                f"Session {session_uuid} still has active task, cleanup postponed"
                            )
                    else:
                        logger.info(
                            f"Session {session_uuid} has active connections - cleanup cancelled"
                        )

                # Clean up the cleanup task reference
                if session_uuid in self.cleanup_tasks:
                    del self.cleanup_tasks[session_uuid]

        except asyncio.CancelledError:
            logger.info(f"Delayed cleanup for session {session_uuid} was cancelled")
            # Clean up the cleanup task reference
            if session_uuid in self.cleanup_tasks:
                del self.cleanup_tasks[session_uuid]
        except Exception as e:
            logger.error(f"Error during delayed cleanup of session {session_uuid}: {e}")
            # Clean up the cleanup task reference
            if session_uuid in self.cleanup_tasks:
                del self.cleanup_tasks[session_uuid]

    async def get_session(
        self, sid: str, session_uuid_str: Optional[str] = None
    ) -> Optional[ChatSession]:
        """Get the chat session for a Socket.IO connection."""
        if sid in self.socket_to_session:
            session_uuid = self.socket_to_session[sid]
            async with self.session_locks[session_uuid]:
                return self.sessions.get(session_uuid)
        if session_uuid_str:
            try:
                session_uuid = uuid.UUID(session_uuid_str)
                async with self.session_locks[session_uuid]:
                    return self.sessions.get(session_uuid)
            except ValueError:
                logger.error(f"Invalid session UUID: {session_uuid_str}")
        return None

    def get_connection_count(self) -> int:
        """Get the number of active connections."""
        return len(self.sessions)
