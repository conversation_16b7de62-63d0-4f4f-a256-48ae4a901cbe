export ENVIRONMENT=dev
# export DATABASE_URL='postgresql+asyncpg://default:<EMAIL>/verceldb?sslmode=require&channel_binding=require'
export DATABASE_URL="postgresql+asyncpg://postgres:postgres@localhost:5432/iiagent"
# export DATABASE_URL='postgresql+asyncpg://iidev:3Ry3Kji*1dY%5D~%5D%7B2@localhost:15432/iiagent'
export GOOGLE_CLIENT_ID=*************-n4nh12r24qlobbm2lmp7k0864ladr4lt.apps.googleusercontent.com
export GOOGLE_CLIENT_SECRET=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
export OAUTHLIB_INSECURE_TRANSPORT=1
export GOOGLE_DISCOVERY_URL=http://localhost:8000/auth/oauth/google/callback
export GOOGLE_APPLICATION_CREDENTIALS="/Users/<USER>/Documents/ii_agent_vertex_ai_service_account.json"

# Complex types like list, set, dict, and sub-models are populated from the environment by treating the environment variable's value as a JSON-encoded string.

export LLM_CONFIGS='{
  "gemini/gemini-2.5-pro": {
    "model": "gemini-2.5-pro",
    "application_model_name": "gemini-2.5-pro",
    "api_key": "AIzaSyAhwXuP2lM0Rjtp-tQHs4Z48iV6StMeaLA",
    "base_url": null,
    "max_retries": 3,
    "max_message_chars": 30000,
    "temperature": 0.0,
    "vertex_region": null,
    "vertex_project_id": null,
    "api_type": "gemini",
    "thinking_tokens": 0,
    "azure_endpoint": null,
    "azure_api_version": null,
    "cot_model": false
  },
  "vertex/claude-sonnet-4-5@********": {
    "model": "claude-sonnet-4-5@********",
    "application_model_name": "claude-4-5-sonnet",
    "api_key": null,
    "base_url": null,
    "max_retries": 3,
    "max_message_chars": 30000,
    "temperature": 0.0,
    "vertex_region": "us-east5",
    "vertex_project_id": "backend-alpha-97077",
    "api_type": "anthropic",
    "thinking_tokens": 10000,
    "azure_endpoint": null,
    "azure_api_version": null,
    "cot_model": false
  },
  "vertex/claude-sonnet-4@20250514": {
    "model": "claude-sonnet-4@20250514",
    "application_model_name": "claude-4-sonnet",
    "api_key": null,
    "base_url": null,
    "max_retries": 3,
    "max_message_chars": 30000,
    "temperature": 0.0,
    "vertex_region": "us-east5",
    "vertex_project_id": "backend-alpha-97077",
    "api_type": "anthropic",
    "thinking_tokens": 10000,
    "azure_endpoint": null,
    "azure_api_version": null,
    "cot_model": false
  },
  "default": {
    "model": "gpt-5",
    "application_model_name": "gpt-5",
    "api_key": "********************************************************************************************************************************************************************",
    "base_url": null,
    "max_retries": 10,
    "max_message_chars": 30000,
    "temperature": 0.0,
    "vertex_region": null,
    "vertex_project_id": null,
    "api_type": "openai",
    "thinking_tokens": 0,
    "azure_endpoint": null,
    "azure_api_version": null,
    "cot_model": false
  }
}'

export AUTO_APPROVE_TOOLS=true
export ACCESS_TOKEN_EXPIRE_MINUTES=43200
export WEB_SEARCH_SERPAPI_API_KEY="bf85cedfd8d0b625420ccf266683f5ca85547e136a0a9cef7b69afb57660d1c9"
export IMAGE_SEARCH_SERPAPI_API_KEY="bf85cedfd8d0b625420ccf266683f5ca85547e136a0a9cef7b69afb57660d1c9"
export WEB_VISIT_FIRECRAWL_API_KEY="fc-9e8a7d0491914b28830b555942769bb4"

export SANDBOX_E2B_API_KEY="e2b_f1f33fac809b1a2f5b2e7804e5da41f8918405ac"
# export SANDBOX_TEMPLATE_ID=idgsc3lghrqno03j8oke
export SANDBOX_TEMPLATE_ID=n794w0o10bq03p86adey
export SANDBOX_REDIS_URL=rediss://default:<EMAIL>:6379

export STORAGE_PROVIDER="gcs"
export FILE_UPLOAD_PROJECT_ID="backend-alpha-97077"
export FILE_UPLOAD_BUCKET_NAME="ii-agent-dev"
export AVATAR_PROJECT_ID="backend-alpha-97077"
export AVATAR_BUCKET_NAME="ii-agent-user-avatar"

export ENHANCE_PROMPT_OPENAI_API_KEY=********************************************************************************************************************************************************************

# export TOOL_SERVER_URL="http://34.143.207.91:1235"
export TOOL_SERVER_URL="https://lived-himself-entities-billion.trycloudflare.com"
# export SANDBOX_SERVER_URL="http://34.143.207.91:8100"
export SANDBOX_SERVER_URL="http://35.202.142.110:8100"

export SLIDE_ASSETS_PROJECT_ID="backend-alpha-97077"
export SLIDE_ASSETS_BUCKET_NAME="ii-agent-public"
export CUSTOM_DOMAIN="sfile.ii.inc"

export RESEARCHER_AGENT_CONFIG='{
  "researcher": {
    "model": "deepseek-reasoner",
    "api_key": "***********************************",
    "base_url" : "https://api.deepseek.com/beta",
    "api_type": "openai"
  },
  "report_builder": {
    "model": "gemini-2.5-flash",
    "api_key": "AIzaSyAhwXuP2lM0Rjtp-tQHs4Z48iV6StMeaLA",
    "base_url": null,
    "max_retries": 10,
    "max_message_chars": 30000,
    "temperature": 0.0,
    "vertex_region": null,
    "vertex_project_id": null,
    "api_type": "gemini",
    "thinking_tokens": 0,
    "azure_endpoint": null,
    "azure_api_version": null,
    "cot_model": false
  },
  "final_report_builder": {
    "model": "gemini-2.5-pro",
    "api_key": "AIzaSyAhwXuP2lM0Rjtp-tQHs4Z48iV6StMeaLA",
    "base_url": null,
    "max_retries": 10,
    "max_message_chars": 30000,
    "temperature": 0.0,
    "vertex_region": null,
    "vertex_project_id": null,
    "api_type": "gemini",
    "thinking_tokens": 0,
    "azure_endpoint": null,
    "azure_api_version": null,
    "cot_model": false
  }
}'


export STRIPE_SECRET_KEY="sk_test_51S1SG9Ry4p6Iz9AOpctNzg5MwqKyC6H3m4wYmK6z3ZGIpf5y6MXwJuj34JHfa6ldiDOMH53h06g0HQnlWFn3cuJv006ycbtJj3"

export STRIPE_PRICE_PLUS_MONTHLY="price_1SBAOeRy4p6Iz9AOFId00b87"
export STRIPE_PRICE_PRO_MONTHLY="price_1SARmzRy4p6Iz9AODZKqnzKr"

export STRIPE_PRICE_PLUS_ANNUALLY="price_1SARlJRy4p6Iz9AOqvApw5XT"
export STRIPE_PRICE_PRO_ANNUALLY="price_1SARnjRy4p6Iz9AOE4E8y88v"

export STRIPE_RETURN_URL=""
export STRIPE_SUCCESS_URL=""
export STRIPE_CANCEL_URL=""
# export STRIPE_WEBHOOK_SECRET="whsec_cm6V71x6aZM8pNApobnJQwJJQdrSJ6SH" #dev
export STRIPE_WEBHOOK_SECRET="whsec_vjjDSogMw8iRlsjAklf67HBj0pj2VVSh" #local

export II_CLIENT_ID="c6ced527-fba4-479d-84b0-6cf46d58bce0"

export II_USE_USERINFO=true
export BETA_PROGRAM_ENABLED=true

# python -m ii_agent.scripts.cron_jobs
python ws_server.py --port 8000
