#!/usr/bin/env bash
# Run the waitlist CSV import helper.
set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
REPO_ROOT="${SCRIPT_DIR}"
DEFAULT_CSV_PATH="${DEFAULT_CSV_PATH:-${REPO_ROOT}/email_waitlist_discord_2025-09-30.csv}"

cd "$REPO_ROOT"

if [[ "$#" -eq 0 ]]; then
  exec python3 -m ii_agent.scripts.import_waitlist --csv "$DEFAULT_CSV_PATH"
else
  exec python3 -m ii_agent.scripts.import_waitlist "$@"
fi
