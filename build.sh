#!/bin/sh

VERSION=$(git rev-parse HEAD)

echo "Building frontend Docker image..."
docker build -t us-central1-docker.pkg.dev/backend-alpha-97077/iirepo/ii-agent-prod-fe:$VERSION -f ./docker/frontend/Dockerfile .
if [ $? -eq 0 ]; then
	echo "Frontend image built successfully. Pushing to registry..."
	docker push us-central1-docker.pkg.dev/backend-alpha-97077/iirepo/ii-agent-prod-fe:$VERSION
else
	echo "Frontend image build failed!" >&2
	exit 1
fi

echo "Building backend Docker image..."
docker build -t us-central1-docker.pkg.dev/backend-alpha-97077/iirepo/ii-agent-prod:$VERSION -f ./docker/backend/Dockerfile .
if [ $? -eq 0 ]; then
	echo "Backend image built successfully. Pushing to registry..."
	docker push us-central1-docker.pkg.dev/backend-alpha-97077/iirepo/ii-agent-prod:$VERSION
else
	echo "Backend image build failed!" >&2
	exit 1
fi