/* WeasyPrint CSS Template for Pandoc */
/* Modern, elegant styling for professional documents */

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap');

/* Page Setup */
@page {
  size: A4;
  margin: 2.5cm 2cm 2cm 2cm;
  
  @top-center {
    content: string(chapter-title);
    font-family: 'Inter', sans-serif;
    font-size: 10pt;
    font-weight: 500;
    color: #64748b;
    border-bottom: 0.5pt solid #e2e8f0;
    padding-bottom: 0.5cm;
  }
  
  @bottom-right {
    content: counter(page) " / " counter(pages);
    font-family: 'Inter', sans-serif;
    font-size: 9pt;
    color: #64748b;
  }
}

/* First page styling */
@page :first {
  margin-top: 4cm;
  @top-center { content: none; }
}

/* Root Variables */
:root {
  --primary-color: #1e293b;
  --secondary-color: #475569;
  --accent-color: #3b82f6;
  --success-color: #059669;
  --warning-color: #d97706;
  --danger-color: #dc2626;
  --background-color: #ffffff;
  --surface-color: #f8fafc;
  --border-color: #e2e8f0;
  --text-color: #334155;
  --text-muted: #64748b;
  --code-bg: #f1f5f9;
}

/* Base Styles */
* {
  box-sizing: border-box;
}

html {
  font-size: 11pt;
  line-height: 1.6;
  color: var(--text-color);
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-weight: 400;
  margin: 0;
  padding: 0;
  background: var(--background-color);
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  color: var(--primary-color);
  margin: 1.5em 0 0.5em 0;
  line-height: 1.3;
  page-break-after: avoid;
}

h1 {
  font-size: 2.2em;
  font-weight: 700;
  margin: 0 0 1em 0;
  padding-bottom: 0.3em;
  border-bottom: 2pt solid var(--accent-color);
  string-set: chapter-title content();
}

h2 {
  font-size: 1.6em;
  margin-top: 2em;
  padding-left: 0.2em;
  border-left: 3pt solid var(--accent-color);
}

h3 {
  font-size: 1.3em;
  color: var(--secondary-color);
}

h4 {
  font-size: 1.1em;
  color: var(--secondary-color);
  font-weight: 500;
}

h5, h6 {
  font-size: 1em;
  color: var(--text-muted);
  font-weight: 500;
}

/* Paragraphs */
p {
  margin: 0 0 1em 0;
  text-align: justify;
  orphans: 3;
  widows: 3;
}

/* Lists */
ul, ol {
  margin: 0 0 1em 0;
  padding-left: 1.5em;
}

li {
  margin: 0.3em 0;
}

ul li::marker {
  color: var(--accent-color);
}

ol li::marker {
  color: var(--accent-color);
  font-weight: 500;
}

/* Links */
a {
  color: var(--accent-color);
  text-decoration: none;
  border-bottom: 1pt solid transparent;
  transition: border-color 0.2s ease;
}

a:hover {
  border-bottom-color: var(--accent-color);
}

/* Code Styling */
code {
  font-family: 'JetBrains Mono', 'SF Mono', Monaco, 'Cascadia Code', monospace;
  font-size: 0.9em;
  background: var(--code-bg);
  padding: 0.1em 0.3em;
  border-radius: 3pt;
  color: var(--primary-color);
  font-weight: 500;
}

pre {
  font-family: 'JetBrains Mono', monospace;
  background: var(--surface-color);
  border: 1pt solid var(--border-color);
  border-left: 3pt solid var(--accent-color);
  border-radius: 4pt;
  padding: 1em;
  margin: 1em 0;
  overflow-x: auto;
  font-size: 0.85em;
  line-height: 1.4;
  page-break-inside: avoid;
}

pre code {
  background: none;
  padding: 0;
  border-radius: 0;
  font-weight: 400;
}

/* Blockquotes */
blockquote {
  margin: 1.5em 0;
  padding: 1em 1.5em;
  background: var(--surface-color);
  border-left: 4pt solid var(--accent-color);
  border-radius: 0 4pt 4pt 0;
  font-style: italic;
  color: var(--secondary-color);
  page-break-inside: avoid;
}

blockquote p:last-child {
  margin-bottom: 0;
}

/* Tables */
table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5em 0;
  font-size: 0.95em;
  page-break-inside: avoid;
}

th, td {
  padding: 0.8em 1em;
  text-align: left;
  border-bottom: 1pt solid var(--border-color);
}

th {
  background: var(--surface-color);
  font-weight: 600;
  color: var(--primary-color);
  border-bottom: 2pt solid var(--accent-color);
}

tr:nth-child(even) {
  background: rgba(248, 250, 252, 0.5);
}

/* Images and Figures */
img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 1.5em auto;
  border-radius: 4pt;
  box-shadow: 0 4pt 6pt rgba(0, 0, 0, 0.1);
}

figure {
  margin: 2em 0;
  text-align: center;
  page-break-inside: avoid;
}

figcaption {
  margin-top: 0.5em;
  font-size: 0.9em;
  color: var(--text-muted);
  font-style: italic;
}

/* Horizontal Rules */
hr {
  border: none;
  height: 1pt;
  background: linear-gradient(to right, transparent, var(--border-color), transparent);
  margin: 2em 0;
}

/* Special Elements */
.title-page {
  text-align: center;
  margin: 4cm 0;
  page-break-after: always;
}

.title-page h1 {
  font-size: 3em;
  margin-bottom: 0.5em;
  border: none;
}

.subtitle {
  font-size: 1.5em;
  color: var(--secondary-color);
  font-weight: 300;
  margin: 1em 0;
}

.author, .date {
  font-size: 1.1em;
  color: var(--text-muted);
  margin: 0.5em 0;
}

/* Callout Boxes */
.callout {
  margin: 1.5em 0;
  padding: 1em 1.5em;
  border-radius: 6pt;
  border-left: 4pt solid;
  page-break-inside: avoid;
}

.callout.info {
  background: rgba(59, 130, 246, 0.05);
  border-left-color: var(--accent-color);
  color: var(--primary-color);
}

.callout.warning {
  background: rgba(217, 119, 6, 0.05);
  border-left-color: var(--warning-color);
}

.callout.danger {
  background: rgba(220, 38, 38, 0.05);
  border-left-color: var(--danger-color);
}

.callout.success {
  background: rgba(5, 150, 105, 0.05);
  border-left-color: var(--success-color);
}

/* Table of Contents */
.toc {
  page-break-after: always;
}

.toc h2 {
  border-left: none;
  padding-left: 0;
}

.toc ul {
  list-style: none;
  padding-left: 0;
}

.toc li {
  margin: 0.5em 0;
  padding-left: 1em;
  text-indent: -1em;
}

.toc a {
  text-decoration: none;
  border-bottom: 1pt dotted var(--border-color);
}

/* Print Optimizations */
@media print {
  * {
    -webkit-print-color-adjust: exact !important;
    color-adjust: exact !important;
  }
  
  body {
    font-size: 10pt;
  }
  
  .page-break {
    page-break-before: always;
  }
  
  .no-break {
    page-break-inside: avoid;
  }
}

/* Pandoc-specific classes */
.pandoc-table {
  margin: 1.5em auto;
  border-top: 2pt solid var(--border-color);
  border-bottom: 2pt solid var(--border-color);
}

.sourceCode {
  background: var(--surface-color);
  border-left: 3pt solid var(--accent-color);
}

/* Footnotes */
.footnotes {
  border-top: 1pt solid var(--border-color);
  margin-top: 3em;
  padding-top: 1em;
  font-size: 0.9em;
}

.footnotes ol {
  padding-left: 1.2em;
}

.footnote-ref {
  font-size: 0.8em;
  vertical-align: super;
  color: var(--accent-color);
}
