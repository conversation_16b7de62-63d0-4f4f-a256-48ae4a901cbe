{"permissions": {"allow": ["Bash(npm install:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(rm:*)", "Bash(npm run build:*)", "Bash(npm run typecheck:*)", "Bash(pnpm build:*)", "Bash(pnpm list:*)", "Bash(pnpm add:*)", "Bash(npm run:*)", "Bash(npx tsc:*)", "Bash(grep:*)", "Bash(npx eslint:*)", "<PERSON><PERSON>(sed:*)", "mcp__ide__getDiagnostics", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(mv:*)"], "deny": []}}