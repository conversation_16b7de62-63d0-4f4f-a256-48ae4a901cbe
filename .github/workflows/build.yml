name: Build and Push Docker Images

on:
  workflow_dispatch: {}

permissions:
  contents: read

env:
  REGION: us-central1
  REGISTRY: us-central1-docker.pkg.dev
  REPO_PATH: backend-alpha-97077/iirepo
  IMAGE_TAG: ${{ github.sha }}

jobs:
  build-and-push:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Authenticate to Google Cloud (Service Account Key)
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}

      - name: Set up gcloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Configure Docker for Artifact Registry
        run: gcloud auth configure-docker $REGISTRY --quiet

      - name: Build frontend image
        run: |
          docker build \
            -t $REGISTRY/$REPO_PATH/ii-agent-prod-fe:$IMAGE_TAG \
            -f ./docker/frontend/Dockerfile .

      - name: Push frontend image
        run: docker push $REGISTRY/$REPO_PATH/ii-agent-prod-fe:$IMAGE_TAG

      - name: Build backend image
        run: |
          docker build \
            -t $REGISTRY/$REPO_PATH/ii-agent-prod:$IMAGE_TAG \
            -f ./docker/backend/Dockerfile .

      - name: Push backend image
        run: docker push $REGISTRY/$REPO_PATH/ii-agent-prod:$IMAGE_TAG
