import { combineReducers } from 'redux'
import { messagesReducer } from './slice/messages'
import { uiReducer } from './slice/ui'
import { editorReducer } from './slice/editor'
import { agentReducer } from './slice/agent'
import { filesReducer } from './slice/files'
import { workspaceReducer } from './slice/workspace'
import { settingsReducer } from './slice/settings'
import { sessionsReducer } from './slice/sessions'
import { userReducer } from './slice/user'
import { favoritesReducer } from './slice/favorites'

export default combineReducers({
    messages: messagesReducer,
    ui: uiReducer,
    editor: editorReducer,
    agent: agentReducer,
    files: filesReducer,
    workspace: workspaceReducer,
    settings: settingsReducer,
    sessions: sessionsReducer,
    user: userReducer,
    favorites: favoritesReducer
})
