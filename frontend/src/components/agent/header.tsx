import { useLocation, useNavigate, useParams } from 'react-router'

import ButtonIcon from '@/components/button-icon'
import { Button } from '@/components/ui/button'
import { Icon } from '@/components/ui/icon'
import { useMemo, useState } from 'react'
import ShareConversation from './share-conversation'
import { ISession } from '@/typings'
import {
    useAppDispatch,
    useAppSelector,
    toggleFavoriteAsync,
    selectIsFavorite
} from '@/state'
import { ENABLE_BETA } from '@/constants/features'

interface AgentHeaderProps {
    sessionData?: ISession
}

const AgentHeader = ({ sessionData }: AgentHeaderProps) => {
    const navigate = useNavigate()
    const dispatch = useAppDispatch()
    const { sessionId } = useParams()
    const location = useLocation()

    const isFavorite = useAppSelector(selectIsFavorite(sessionId || ''))
    const [isShareOpen, setIsShareOpen] = useState(false)

    const isShareMode = useMemo(
        () => location.pathname.includes('/share/'),
        [location.pathname]
    )

    const handleShare = () => {
        if (!sessionId) return
        setIsShareOpen(true)
    }

    const handleBack = () => {
        navigate('/')
    }

    const handleToggleFavorite = () => {
        if (sessionId) {
            dispatch(toggleFavoriteAsync(sessionId))
        }
    }

    return (
        <div className="py-3 px-6 flex items-center gap-x-4 border-b border-neutral-200 dark:border-white/30">
            <ButtonIcon
                name="home"
                className="bg-black"
                iconClassName="fill-sky-blue-2 dark:fill-black"
                onClick={handleBack}
            />
            <div className="relative flex items-center gap-x-[6px]">
                <img
                    src="/images/logo-only.svg"
                    className="size-6 hidden dark:inline"
                    alt="Logo"
                />
                <img
                    src="/images/logo-charcoal.svg"
                    className="size-6 inline dark:hidden"
                    alt="Logo"
                />
                <span className="text-black dark:text-white text-sm font-semibold">
                    II-Agent
                </span>
                {ENABLE_BETA && (
                    <span className="text-[10px] absolute -right-8 -top-1">
                        BETA
                    </span>
                )}
            </div>
            {sessionData?.name && (
                <div className="flex gap-x-4 items-center absolute left-1/2 -translate-x-1/2">
                    <div className="flex items-center gap-x-2">
                        <div className="border dark:border-white rounded-full size-6 flex items-center justify-center">
                            <Icon
                                name="lock"
                                className="fill-black dark:fill-white"
                            />
                        </div>
                        <span className="dark:text-white font-semibold text-sm flex-1 line-clamp-1">
                            {sessionData?.name}
                        </span>
                    </div>
                    {!isShareMode && (
                        <>
                            <Button
                                size="icon"
                                className="w-auto"
                                onClick={handleShare}
                            >
                                <Icon
                                    name="share"
                                    className="stroke-black dark:stroke-white size-[18px]"
                                />
                            </Button>
                            <Button
                                size="icon"
                                className="w-auto"
                                onClick={handleToggleFavorite}
                            >
                                {isFavorite ? (
                                    <Icon
                                        name="star-fill"
                                        className="fill-white dark:fill-white size-[18px]"
                                    />
                                ) : (
                                    <Icon
                                        name="star"
                                        className="fill-black dark:fill-white size-[18px]"
                                    />
                                )}
                            </Button>
                        </>
                    )}
                </div>
            )}
            <ShareConversation
                open={isShareOpen}
                onOpenChange={setIsShareOpen}
            />
        </div>
    )
}

export default AgentHeader
