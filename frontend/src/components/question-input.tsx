import { useCallback, useEffect, useRef, useState } from 'react'

import { Textarea } from './ui/textarea'
import ButtonIcon from './button-icon'
import { useUploadFiles, type FileUploadStatus } from '@/hooks/use-upload-files'
import FilesPreview from './question-files-preview'
import Suggestions from './question-suggestions'
import FeatureSelector from './question-feature-selector'
import EnhanceButton from './question-enhance-button'
import SubmitButton from './question-submit-button'
import {
    selectRequireClearFiles,
    setRequireClearFiles,
    selectSelectedFeature,
    setSelectedFeature,
    selectShouldFocusInput,
    setShouldFocusInput,
    useAppDispatch,
    useAppSelector
} from '@/state'
//
import { useParams } from 'react-router'
import { AGENT_TYPE } from '@/typings'
import { FEATURES } from '@/constants/tool'
import { Button } from './ui/button'
import { Icon } from './ui/icon'
import { Toolt<PERSON>, TooltipContent, TooltipTrigger } from './ui/tooltip'

interface QuestionInputProps {
    value: string
    setValue: (value: string) => void
    handleKeyDown: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void
    handleSubmit: (question: string) => void
    className?: string
    textareaClassName?: string
    placeholder?: string
    isDisabled?: boolean
    handleEnhancePrompt?: () => void
    handleCancel?: () => void
    onFilesChange?: (filesCount: number) => void
    hideSuggestions?: boolean
    hideFeatureSelector?: boolean
}

const QuestionInput = ({
    className,
    textareaClassName,
    placeholder,
    value,
    setValue,
    handleKeyDown,
    handleSubmit,
    isDisabled,
    handleEnhancePrompt,
    handleCancel,
    onFilesChange,
    hideSuggestions,
    hideFeatureSelector
}: QuestionInputProps) => {
    const dispatch = useAppDispatch()
    const requireClearFiles = useAppSelector(selectRequireClearFiles)
    const selectedFeature = useAppSelector(selectSelectedFeature)
    const shouldFocusInput = useAppSelector(selectShouldFocusInput)
    const isUploading = useAppSelector((state) => state.files.isUploading)
    const isLoading = useAppSelector((state) => state.ui.isLoading)
    const isGeneratingPrompt = useAppSelector(
        (state) => state.ui.isGeneratingPrompt
    )
    const isCreatingSession = useAppSelector(
        (state) => state.ui.isCreatingSession
    )
    const { sessionId } = useParams()

    const textareaRef = useRef<HTMLTextAreaElement>(null)
    const fileInputRef = useRef<HTMLInputElement>(null)

    const [files, setFiles] = useState<FileUploadStatus[]>([])
    const [currentTextareaValue, setCurrentTextareaValue] = useState(value)

    const {
        handleRemoveFile,
        handleFileUploadWithSignedUrl,
        handlePastedImageUpload
    } = useUploadFiles()

    const removeFile = (fileName: string) => {
        handleRemoveFile(fileName)
        setFiles((prev) => prev.filter((file) => file.name !== fileName))
    }

    // Handle key down events with auto-scroll for Shift+Enter
    const handleKeyDownWithAutoScroll = (
        e: React.KeyboardEvent<HTMLTextAreaElement>
    ) => {
        if (!currentTextareaValue.trim() || isDisabled || isCreatingSession)
            return

        if (e.key === 'Enter') {
            if (e.shiftKey) {
                // Check if cursor is at the last line before allowing default behavior
                const textarea = textareaRef.current
                if (textarea) {
                    const cursorPosition = textarea.selectionStart
                    const text = textarea.value

                    // Check if cursor is at or near the end of the text
                    const isAtLastLine = !text
                        .substring(cursorPosition)
                        .includes('\n')

                    // Allow default behavior for Shift+Enter (new line)
                    // Only schedule auto-scroll if we're at the last line
                    if (isAtLastLine) {
                        setTimeout(() => {
                            if (textarea) {
                                textarea.scrollTop = textarea.scrollHeight
                            }
                        }, 0)
                    }
                }
            } else {
                // For Enter key, get current value from textarea and pass to handleSubmit
                e.preventDefault()
                const currentValue = textareaRef.current?.value || ''
                if (currentValue.trim()) {
                    handleSubmit(currentValue)
                    // Clear the textarea after submission
                    if (textareaRef.current && sessionId) {
                        textareaRef.current.value = ''
                        setCurrentTextareaValue('')
                    }
                }
            }
        } else {
            // Pass other key events to the original handler, but modify to work with uncontrolled input
            const modifiedEvent = {
                ...e,
                target: {
                    ...e.target,
                    value: textareaRef.current?.value || ''
                }
            } as React.KeyboardEvent<HTMLTextAreaElement>
            handleKeyDown(modifiedEvent)
        }
    }

    const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
        if (!e.target.files) return

        const filesToUpload = Array.from(e.target.files)
        await handleFileUploadWithSignedUrl(filesToUpload, setFiles)

        // Clear the input
        e.target.value = ''
    }

    // Handle clipboard paste for images
    const handlePaste = useCallback(
        async (e: React.ClipboardEvent<HTMLTextAreaElement>) => {
            const clipboardItems = e.clipboardData?.items
            if (!clipboardItems) return

            const imageItems = Array.from(clipboardItems).filter((item) =>
                item.type.startsWith('image/')
            )

            if (imageItems.length === 0) return

            // Prevent default paste behavior for images
            e.preventDefault()

            for (const item of imageItems) {
                const file = item.getAsFile()
                if (!file) continue

                // Generate a unique filename for the pasted image
                const timestamp = Date.now()
                const extension = file.type.split('/')[1] || 'png'
                const fileName = `pasted-image-${timestamp}.${extension}`

                // Create a new File object with the generated name
                const renamedFile = new File([file], fileName, {
                    type: file.type
                })

                await handlePastedImageUpload(renamedFile, fileName, setFiles)
            }
        },
        [handlePastedImageUpload, setFiles]
    )

    const handleSelectFeature = (type: string) => {
        dispatch(setSelectedFeature(type))
        setTimeout(() => {
            textareaRef.current?.focus()
        }, 300)
    }

    const removeFeature = () => {
        dispatch(setSelectedFeature(AGENT_TYPE.GENERAL))
    }

    useEffect(() => {
        if (onFilesChange) {
            onFilesChange(files.length)
        }
    }, [files, onFilesChange])

    useEffect(() => {
        if (requireClearFiles) {
            files.forEach((file) => {
                if (file.preview) URL.revokeObjectURL(file.preview)
            })
            setFiles([])

            // Reset the flag
            dispatch(setRequireClearFiles(false))
        }
    }, [requireClearFiles, dispatch, files])

    // Clean up object URLs when component unmounts
    useEffect(() => {
        return () => {
            files.forEach((file) => {
                if (file.preview) URL.revokeObjectURL(file.preview)
            })
        }
    }, [files])

    useEffect(() => {
        setValue(textareaRef.current?.value || '')
    }, [textareaRef.current?.value])

    // Add effect to sync textarea with external value changes
    useEffect(() => {
        if (textareaRef.current && textareaRef.current.value !== value) {
            textareaRef.current.value = value
            setCurrentTextareaValue(value)
        }
    }, [value])

    // Handle auto-focus when shouldFocusInput is triggered
    useEffect(() => {
        if (shouldFocusInput && textareaRef.current) {
            // Small delay to ensure DOM is ready after navigation
            setTimeout(() => {
                if (textareaRef.current?.value) {
                    textareaRef.current.value = ''
                    setCurrentTextareaValue('')
                }
                textareaRef.current?.focus()
                // Reset the focus trigger
                dispatch(setShouldFocusInput(false))
            }, 100)
        }
    }, [shouldFocusInput, dispatch])

    return (
        <div className={`relative ${className}`}>
            <FilesPreview
                files={files}
                isUploading={isUploading}
                onRemove={removeFile}
            />
            <div className="relative">
                <Textarea
                    ref={textareaRef}
                    className={`w-full p-4 pb-[72px] rounded-xl resize-none !placeholder-black/[0.48] dark:!placeholder-white/40 !bg-grey-3 dark:!bg-black border-2 border-grey  ${
                        files.length > 0
                            ? 'pt-[72px] !min-h-[240px]'
                            : 'min-h-[167px]'
                    } max-h-[400px] ${textareaClassName}`}
                    placeholder={
                        placeholder || 'Describe what you want to accomplish...'
                    }
                    defaultValue={value}
                    onChange={(e) => {
                        const newValue = e.target.value
                        setCurrentTextareaValue(newValue)
                    }}
                    onKeyDown={handleKeyDownWithAutoScroll}
                    onPaste={handlePaste}
                />
            </div>
            <div className="absolute bottom-0 left-0 px-4 w-full">
                {!hideSuggestions && (
                    <Suggestions
                        hidden={!!currentTextareaValue.trim()}
                        agentType={selectedFeature}
                        onSelect={(item) => {
                            if (textareaRef.current) {
                                textareaRef.current.value = item
                                setCurrentTextareaValue(item)
                                setTimeout(() => {
                                    textareaRef.current?.focus()
                                }, 300)
                            }
                        }}
                    />
                )}
                <div className="flex items-center justify-between !bg-grey-3 dark:!bg-black py-4 mb-[2px]">
                    <div className="flex items-center gap-x-3 justify-between">
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <ButtonIcon
                                    name="link"
                                    onClick={() =>
                                        fileInputRef.current?.click()
                                    }
                                />
                            </TooltipTrigger>
                            <TooltipContent>Add photos & files</TooltipContent>
                        </Tooltip>

                        <input
                            ref={fileInputRef}
                            id="file-upload"
                            type="file"
                            multiple
                            className="hidden"
                            onChange={handleFileChange}
                            disabled={
                                isUploading || (sessionId ? isLoading : false)
                            }
                        />

                        <EnhanceButton
                            isGenerating={isGeneratingPrompt}
                            onClick={() => {
                                if (handleEnhancePrompt) handleEnhancePrompt()
                            }}
                            disabled={
                                isGeneratingPrompt ||
                                !currentTextareaValue.trim() ||
                                isDisabled ||
                                isLoading ||
                                isUploading
                            }
                        />

                        <FeatureSelector
                            hide={hideFeatureSelector}
                            selectedFeature={selectedFeature}
                            onRemove={removeFeature}
                            onSelect={handleSelectFeature}
                        />
                    </div>
                    <SubmitButton
                        isLoading={isLoading}
                        isCreatingSession={isCreatingSession}
                        disabled={
                            !currentTextareaValue.trim() ||
                            isDisabled ||
                            isCreatingSession
                        }
                        onCancel={handleCancel}
                        onSubmit={() => {
                            const currentValue =
                                textareaRef.current?.value || ''
                            if (currentValue.trim()) {
                                handleSubmit(currentValue)
                                if (textareaRef.current && sessionId) {
                                    textareaRef.current.value = ''
                                    setCurrentTextareaValue('')
                                }
                            }
                        }}
                    />
                </div>
            </div>
            {!hideFeatureSelector && selectedFeature === AGENT_TYPE.GENERAL && (
                <div className="flex items-center justify-center absolute w-full -bottom-14 z-10">
                    <div className="flex items-center gap-x-4 justify-center">
                        {FEATURES.map((feature) => (
                            <Button
                                variant="outline"
                                key={feature.name}
                                onClick={() =>
                                    handleSelectFeature(feature.type)
                                }
                                className="h-8 !px-4 cursor-pointer rounded-full text-xs border-firefly dark:border-sky-blue text-sky-blue dark:text-sky-blue"
                            >
                                <Icon
                                    name={feature.icon}
                                    className="size-4 fill-black dark:fill-sky-blue"
                                />
                                {feature.name}
                            </Button>
                        ))}
                    </div>
                </div>
            )}
        </div>
    )
}

export default QuestionInput
