import { useNavigate } from 'react-router'
import { useMemo, useState } from 'react'
import { toast } from 'sonner'
import dayjs from 'dayjs'

import { useAppSelector } from '@/state/store'
import {
    selectSubscriptionCurrentPeriodEnd,
    selectSubscriptionPlan,
    selectSubscriptionStatus,
    selectUser
} from '@/state/slice/user'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { getFirstCharacters } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { useAuth } from '@/contexts/auth-context'
import { billingService } from '@/services/billing.service'
import { SubscriptionPlan } from '@/typings/subscription'
import { SUBSCRIPTION_PLANS } from '@/constants/subscription'

const AccountTab = () => {
    const user = useAppSelector(selectUser)
    const { logout } = useAuth()
    const navigate = useNavigate()
    const [isManaging, setIsManaging] = useState(false)

    const status = useAppSelector(selectSubscriptionStatus)
    const currentPeriodEnd = useAppSelector(selectSubscriptionCurrentPeriodEnd)
    const plan = useAppSelector(selectSubscriptionPlan) ?? SubscriptionPlan.Free

    const formattedPeriodEnd = useMemo(() => {
        if (!currentPeriodEnd || (status !== 'active' && status !== 'paid'))
            return 'Not available'

        const parsed = dayjs(currentPeriodEnd)
        return parsed.isValid()
            ? parsed.format('MMMM D, YYYY')
            : 'Not available'
    }, [currentPeriodEnd, status])

    const handleLogout = () => {
        logout()
        navigate('/login')
    }

    const handleManageSubscription = async () => {
        if (isManaging) return

        try {
            setIsManaging(true)
            const { url } = await billingService.createPortalSession({
                returnUrl: window.location.href
            })
            if (!url) {
                toast.error('Unable to open Stripe portal. Please try again.')
                return
            }

            window.open(url)
        } catch (error) {
            console.error('Failed to create Stripe portal session', error)
            toast.error('Unable to open Stripe portal. Please try again.')
        } finally {
            setIsManaging(false)
        }
    }

    return (
        <div className="space-y-6 pt-2">
            <div className="flex items-center gap-4 mb-6">
                <Avatar className="size-14">
                    <AvatarImage src={user?.avatar} />
                    <AvatarFallback>
                        {user?.first_name
                            ? getFirstCharacters(
                                  `${user?.first_name} ${user?.last_name}`
                              )
                            : `II`}
                    </AvatarFallback>
                </Avatar>
                <div className="space-y-1">
                    <p className="text-[18px] font-semibold">{`${user?.first_name} ${user?.last_name}`}</p>
                    <p className="text-sm">{user?.email}</p>
                </div>
            </div>

            <div className="space-y-4">
                <h2 className="text-[18px] font-semibold mb-4">
                    Linked Accounts
                </h2>
                <div className="flex items-center justify-between">
                    <p className="text-sm">Google</p>
                    <Button className="text-black/[0.56] dark:text-white/[0.56] underline p-0 h-auto">
                        Connect your account
                    </Button>
                </div>
                <div className="flex items-center justify-between">
                    <p className="text-sm">Microsoft</p>
                    <Button className="text-black/[0.56] dark:text-white/[0.56] underline p-0 h-auto">
                        Connect your account
                    </Button>
                </div>
                <div className="flex items-center justify-between">
                    <p className="text-sm">GitHub</p>
                    <Button className="text-black/[0.56] dark:text-white/[0.56] underline p-0 h-auto">
                        Connect your account
                    </Button>
                </div>
                <div className="flex items-center justify-between">
                    <p className="text-sm">Apple</p>
                    <Button className="text-black/[0.56] dark:text-white/[0.56] underline p-0 h-auto">
                        Connect your account
                    </Button>
                </div>
            </div>

            {plan !== SubscriptionPlan.Free && (
                <div className="pt-6 border-t border-black/30 dark:border-white/30">
                    <h2 className="text-[18px] font-semibold mb-2">
                        Payment & Invoices
                    </h2>
                    <div className="space-y-4">
                        <div className="flex items-center gap-x-4">
                            <Button
                                size="xl"
                                className="bg-firefly text-sky-blue-2 dark:bg-sky-blue dark:text-black font-semibold w-[247px]"
                                onClick={handleManageSubscription}
                            >
                                Manage
                            </Button>
                        </div>
                    </div>
                </div>
            )}

            {plan !== SubscriptionPlan.Free && (
                <div className="pt-6 border-t border-black/30 dark:border-white/30">
                    <h2 className="text-[18px] font-semibold mb-2">
                        {`II-Agent ${SUBSCRIPTION_PLANS[plan].name} Plan`}
                    </h2>
                    <div className="space-y-4">
                        <p className="text-sm text-black dark:text-white">
                            Your plan auto-renews on {formattedPeriodEnd}.
                        </p>
                        <div className="flex items-center gap-x-4">
                            <Button
                                size="xl"
                                className="bg-firefly text-sky-blue-2 dark:bg-sky-blue dark:text-black font-semibold w-[247px]"
                                onClick={handleManageSubscription}
                            >
                                Manage
                            </Button>
                        </div>
                    </div>
                </div>
            )}

            <div className="py-6 border-t border-black/30 dark:border-white/30">
                <h2 className="text-[18px] font-semibold mb-4">Security</h2>
                <div className="flex items-center gap-x-4">
                    <Button
                        size="xl"
                        className="bg-firefly text-sky-blue-2 dark:bg-sky-blue dark:text-black font-semibold w-[247px]"
                        onClick={handleLogout}
                    >
                        Log out
                    </Button>

                    {/* <Button
                        size="xl"
                        variant="outline"
                        className="text-red border-red w-[247px]"
                    >
                        Delete account
                    </Button> */}
                </div>
            </div>
        </div>
    )
}

export default AccountTab
