import { useEffect, useState } from 'react'

import { settingsService } from '@/services/settings.service'
import { useAppSelector } from '@/state'
import { ISetting } from '@/typings'
import { toast } from 'sonner'
import { Button } from '../ui/button'
import { Icon } from '../ui/icon'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { Sheet, SheetClose, SheetContent, SheetHeader } from '../ui/sheet'
import { Textarea } from '../ui/textarea'

interface ClaudeCodeSettingProps {
    open: boolean
    onOpenChange: (open: boolean) => void
    onSaveConfig: (data: ISetting) => void
}

const ClaudeCodeSetting = ({
    open,
    onOpenChange,
    onSaveConfig
}: ClaudeCodeSettingProps) => {
    const isSavingSetting = useAppSelector(
        (state) => state.settings.isSavingSetting
    )
    const currentSettingData = useAppSelector(
        (state) => state.settings.currentSettingData
    )

    const [authJson, setAuth<PERSON>son] = useState('')
    const [apiKey, setApiKey] = useState('')

    const handleCancel = () => {
        onOpenChange(false)
    }

    const handleSaveConfig = async () => {
        // Require at least one of authJson or apiKey
        if (!authJson.trim() && !apiKey.trim()) {
            toast.warning('Please provide either Auth JSON or API Key')
            return
        }

        // Build payload conditionally
        const payload: {
            auth_json?: Record<string, unknown>
            apikey?: string
        } = {}

        // Validate and attach auth_json only if provided
        if (authJson.trim()) {
            try {
                payload.auth_json = JSON.parse(authJson)
            } catch {
                toast.warning('Invalid JSON format for auth configuration')
                return
            }
        }

        if (apiKey.trim()) payload.apikey = apiKey.trim()

        try {
            // Use the settings service with the new configureClaudeCode method
            // This will create or update the Claude Code configuration and set is_active to true
            await settingsService.configureClaudeCode(payload)
            toast.success(
                'Claude Code configuration saved and activated successfully'
            )
            onOpenChange(false)

            // Update the settings with claude_code enabled
            const newSettings = {
                ...currentSettingData,
                claude_code: true // Set to true since we just activated it
            }
            onSaveConfig(newSettings)
        } catch (error: any) {
            const errorMessage =
                error.response?.data?.detail ||
                'Failed to save Claude Code configuration'
            toast.error(errorMessage)
        }
    }

    useEffect(() => {
        // Load existing Claude Code settings when component opens
        const loadClaudeCodeSettings = async () => {
            if (open) {
                try {
                    const claudeCodeSetting =
                        await settingsService.getClaudeCodeSettings()

                    if (claudeCodeSetting) {
                        if (claudeCodeSetting.metadata) {
                            if (claudeCodeSetting.metadata.auth_json) {
                                setAuthJson(
                                    JSON.stringify(
                                        claudeCodeSetting.metadata.auth_json,
                                        null,
                                        2
                                    )
                                )
                            } else {
                                setAuthJson('')
                            }

                            if (claudeCodeSetting.metadata.apikey) {
                                setApiKey(claudeCodeSetting.metadata.apikey)
                            } else {
                                setApiKey('')
                            }
                        } else {
                            setAuthJson('')
                            setApiKey('')
                        }
                    } else {
                        // Clear fields if no existing settings
                        setAuthJson('')
                        setApiKey('')
                    }
                } catch (error) {
                    console.error('Error loading Claude Code settings:', error)
                }
            }
        }

        loadClaudeCodeSettings()
    }, [open])

    return (
        <Sheet open={open} onOpenChange={onOpenChange}>
            <SheetContent className="px-6 pt-12 w-full !max-w-[560px]">
                <SheetHeader className="p-0 gap-6 pb-4">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-x-2">
                            <Icon name="claude-code" className="size-12" />
                            <div className="space-y-1">
                                <p className="text-2xl font-semibold dark:text-white">
                                    Claude Code
                                </p>
                                <p className="text-base dark:text-white/[0.56]">
                                    Anthropic
                                </p>
                            </div>
                        </div>
                        <div className="flex items-center gap-x-4">
                            <SheetClose className="cursor-pointer">
                                <Icon
                                    name="arrow-right"
                                    className="dark:inline hidden"
                                />
                                <Icon
                                    name="arrow-right-dark"
                                    className="dark:hidden inline"
                                />
                            </SheetClose>
                        </div>
                    </div>
                </SheetHeader>
                <div className="overflow-auto pb-12">
                    <p className="dark:text-white text-lg font-semibold">
                        About
                    </p>
                    <p className="dark:text-white text-sm mt-3">
                        Enable Claude Code for autonomous code generation and
                        review
                    </p>
                    <Button
                        className="h-[22px] bg-firefly dark:bg-sky-blue-2 text-sky-blue-2 dark:text-black gap-x-[6px] mt-4 text-xs rounded-full !font-normal"
                        onClick={() =>
                            window.open(
                                `https://www.anthropic.com/claude/code`,
                                '_blank'
                            )
                        }
                    >
                        <Icon
                            name="global"
                            className="size-4 fill-sky-blue-2 dark:fill-black"
                        />
                        Remote
                    </Button>

                    <p className="dark:text-white text-lg font-semibold mt-6">
                        Auth Json
                    </p>
                    <p className="text-sm mt-3">
                        Find your auth json at ~/.claude-code/auth.json (on
                        Windows: C://Users/<USER>/.claude-code/auth.json)
                    </p>

                    <div className="space-y-2 relative mt-3">
                        <Icon
                            name="key-square"
                            className={`absolute top-3 left-4 fill-black dark:fill-white ${authJson ? '' : 'opacity-30'}`}
                        />
                        <Textarea
                            id="auth-json"
                            className="pl-[56px] min-h-[144px] mb-4"
                            placeholder="Enter Claude Code Auth Json"
                            value={authJson}
                            onChange={(e) => setAuthJson(e.target.value)}
                        />
                    </div>

                    <div className="mt-6 space-y-4">
                        <div className="space-y-2">
                            <Label
                                htmlFor="claude-code-apikey"
                                className="dark:text-white text-sm"
                            >
                                API Key
                            </Label>
                            <Input
                                id="claude-code-apikey"
                                type="password"
                                placeholder="Enter API Key"
                                value={apiKey}
                                onChange={(e) => setApiKey(e.target.value)}
                            />
                        </div>
                    </div>
                    <div className="space-y-4 grid grid-cols-2 gap-4 mt-6">
                        <Button
                            type="button"
                            variant="outline"
                            className="h-12 rounded-xl text-base"
                            onClick={handleCancel}
                        >
                            Cancel
                        </Button>
                        <Button
                            className="h-12 rounded-xl bg-sky-blue text-black text-base"
                            disabled={isSavingSetting}
                            onClick={() => handleSaveConfig()}
                        >
                            {isSavingSetting ? 'Saving...' : 'Save'}
                        </Button>
                    </div>
                </div>
            </SheetContent>
        </Sheet>
    )
}

export default ClaudeCodeSetting
