import { type ReactNode, useCallback, useMemo, useState } from 'react'
import { toast } from 'sonner'

import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { getStripe } from '@/lib/stripe'
import { billingService } from '@/services/billing.service'
import {
    SubscriptionPlan,
    type BillingCycle,
    type SubscriptionPlanId
} from '@/typings/subscription'
import {
    selectSubscriptionBillingCycle,
    selectSubscriptionPlan
} from '@/state/slice/user'
import { useAppSelector } from '@/state'
import { Icon } from './ui/icon'

type PlanFeature = {
    id: string
    content: ReactNode
    icon?: 'star' | 'sparkles'
}

type Plan = {
    id: SubscriptionPlanId
    name: string
    isRecommended?: boolean
    highlight?: boolean
    description?: string
    prices: Record<BillingCycle, number>
    features: PlanFeature[]
}

type UpgradePlanProps = {
    className?: string
    title?: ReactNode
    subtitle?: ReactNode
}

// const BILLING_OPTIONS: Array<{
//     id: BillingCycle
//     label: string
//     helper?: string
// }> = [
//     { id: 'monthly', label: 'Monthly' },
//     { id: 'annually', label: 'Annually', helper: '(-10%)' }
// ]

const PLANS: Plan[] = [
    {
        id: SubscriptionPlan.Free,
        name: 'Free',
        prices: {
            monthly: 0,
            annually: 0
        },
        features: [
            {
                id: 'credit',
                content: '300 credits/month'
            },
            {
                id: 'full-stack-web-development',
                content: 'Full Stack Web Development'
            },
            {
                id: 'bring-your-own-key',
                content: 'Bring Your Own Key (BYOK)'
            },
            {
                id: 'general-task-solving',
                content: 'General Task Solving'
            },
            {
                id: 'customize-any-mcp',
                content: 'Customize any MCP'
            },
            {
                id: 'slide-creation',
                content: 'Slide Creation'
            },
            {
                id: 'image-video-generation',
                content: 'Image/Video Generation'
            }
        ]
    },
    {
        id: SubscriptionPlan.Plus,
        name: 'Plus',
        isRecommended: true,
        highlight: true,
        prices: {
            monthly: 30,
            annually: 27
        },
        features: [
            {
                id: 'credit',
                content: '2,000 credits/month'
            },
            {
                id: 'full-stack-web-development',
                content: 'Full Stack Web Development'
            },
            {
                id: 'bring-your-own-key',
                content: 'Bring Your Own Key (BYOK)'
            },
            {
                id: 'general-task-solving',
                content: 'General Task Solving'
            },
            {
                id: 'customize-any-mcp',
                content: 'Customize any MCP'
            },
            {
                id: 'slide-creation',
                content: 'Slide Creation'
            },
            {
                id: 'image-video-generation',
                content: 'Image/Video Generation'
            }
        ]
    },
    {
        id: SubscriptionPlan.Pro,
        name: 'Pro',
        prices: {
            monthly: 120,
            annually: 138
        },
        features: [
            {
                id: 'credit',
                content: '10,000 credits/month'
            },
            {
                id: 'full-stack-web-development',
                content: 'Full Stack Web Development'
            },
            {
                id: 'bring-your-own-key',
                content: 'Bring Your Own Key (BYOK)'
            },
            {
                id: 'general-task-solving',
                content: 'General Task Solving'
            },
            {
                id: 'customize-any-mcp',
                content: 'Customize any MCP'
            },
            {
                id: 'slide-creation',
                content: 'Slide Creation'
            },
            {
                id: 'image-video-generation',
                content: 'Image/Video Generation'
            }
        ]
    }
]

function formatPrice(value: number) {
    return value.toLocaleString('en-US', {
        minimumFractionDigits: Number.isInteger(value) ? 0 : 2,
        maximumFractionDigits: 2
    })
}

export function UpgradePlan({ className }: UpgradePlanProps) {
    const subscriptionPlan = useAppSelector(selectSubscriptionPlan)
    const subscriptionBillingCycle = useAppSelector(
        selectSubscriptionBillingCycle
    )
    // const [billingCycle, setBillingCycle] = useState<BillingCycle>('monthly')
    const billingCycle = 'monthly'
    const [loadingPlan, setLoadingPlan] = useState<SubscriptionPlanId | null>(
        null
    )

    const plans = useMemo(() => PLANS, [])

    const handleUpgrade = useCallback(
        async (planId: SubscriptionPlanId) => {
            if (planId === subscriptionPlan) {
                return
            }

            try {
                setLoadingPlan(planId)

                const checkoutSession =
                    await billingService.createCheckoutSession({
                        planId,
                        billingCycle
                    })

                if (checkoutSession?.url) {
                    window.location.href = checkoutSession.url
                    return
                }

                if (!checkoutSession?.sessionId) {
                    throw new Error('Checkout session identifier missing')
                }

                const stripe = await getStripe()
                if (!stripe) {
                    throw new Error('Unable to initialize Stripe client')
                }

                const { error } = await stripe.redirectToCheckout({
                    sessionId: checkoutSession.sessionId
                })

                if (error) {
                    throw error
                }
            } catch (error) {
                console.error('Failed to start checkout session', error)
                toast.error('Unable to start checkout. Please try again.')
            } finally {
                setLoadingPlan(null)
            }
        },
        [billingCycle, subscriptionPlan]
    )

    return (
        <section className={cn('flex w-full flex-col gap-6', className)}>
            {/* <div className="flex flex-col gap-4 text-left md:text-center">
                <div className="flex justify-center pt-2">
                    <div className="flex rounded-full border border-sky-blue-2 p-1">
                        {BILLING_OPTIONS.map((option) => {
                            const isActive = billingCycle === option.id
                            return (
                                <button
                                    key={option.id}
                                    type="button"
                                    onClick={() => setBillingCycle(option.id)}
                                    className={cn(
                                        'flex items-center gap-2 rounded-full px-4 py-[6px] text-sm font-semibold transition-colors',
                                        isActive
                                            ? 'bg-sky-blue-2 text-black'
                                            : 'text-white hover:text-white'
                                    )}
                                >
                                    {option.label}
                                    {option.helper && (
                                        <span className="text-sm font-semibold uppercase tracking-wide">
                                            {option.helper}
                                        </span>
                                    )}
                                </button>
                            )
                        })}
                    </div>
                </div>
            </div> */}

            <div className="grid grid-cols-3 gap-6">
                {plans.map((plan) => {
                    const isCurrentPlan = plan.id === subscriptionPlan
                    const matchesCurrentBillingCycle =
                        isCurrentPlan &&
                        (!subscriptionBillingCycle ||
                            subscriptionBillingCycle === billingCycle)

                    const price = plan.prices[billingCycle]
                    const priceSuffix = '/month'
                    const shouldHideUpgrade =
                        (subscriptionPlan === SubscriptionPlan.Plus &&
                            plan.id === SubscriptionPlan.Free) ||
                        (subscriptionPlan === SubscriptionPlan.Pro &&
                            plan.id !== SubscriptionPlan.Pro)

                    return (
                        <article
                            key={plan.id}
                            className={cn(
                                'w-[240px] flex h-full flex-col rounded-xl border border-black dark:border-white p-4'
                            )}
                        >
                            <div className="flex flex-col gap-6">
                                <div>
                                    <div className="flex items-start justify-between gap-4">
                                        <div>
                                            <h3 className="text-lg font-semibold">
                                                {plan.name}
                                            </h3>
                                        </div>
                                        {plan.isRecommended && (
                                            <span
                                                className={cn(
                                                    'w-12 h-6 flex items-center justify-center rounded-full bg-yellow'
                                                )}
                                            >
                                                {plan.highlight && (
                                                    <Icon
                                                        name="thumbsup"
                                                        className="size-4"
                                                    />
                                                )}
                                            </span>
                                        )}
                                    </div>
                                    {plan.description && (
                                        <p className="mt-1 text-sm">
                                            {plan.description}
                                        </p>
                                    )}
                                </div>

                                <div>
                                    <div className="flex items-center gap-1">
                                        <span className="text-lg ">$</span>
                                        <div className="flex items-end gap-1">
                                            <span className="text-[32px] h-[42px] font-bold text-red">
                                                {formatPrice(price)}
                                            </span>
                                            <span className="text-base">
                                                {priceSuffix}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <ul className="mt-8 flex flex-1 flex-col gap-3 text-sm">
                                {plan.features.map((feature) => (
                                    <li
                                        key={feature.id}
                                        className="flex items-start gap-2"
                                    >
                                        <span className="">
                                            <Icon
                                                name="star"
                                                className="fill-black dark:fill-white size-4"
                                            />
                                        </span>
                                        <span className="text-10">
                                            {feature.content}
                                        </span>
                                    </li>
                                ))}
                            </ul>

                            {!shouldHideUpgrade && (
                                <Button
                                    variant="outline"
                                    size="xl"
                                    disabled={
                                        matchesCurrentBillingCycle ||
                                        loadingPlan === plan.id
                                    }
                                    onClick={() => handleUpgrade(plan.id)}
                                    className={cn(
                                        'mt-10 w-full rounded-xl border text-base font-semibold transition-colors',
                                        {
                                            'opacity-80':
                                                loadingPlan === plan.id,
                                            'border-transparent bg-sky-blue-4 text-black':
                                                !matchesCurrentBillingCycle &&
                                                loadingPlan !== plan.id,
                                            'cursor-default border-black dark:border-[#55d8ff] bg-transparent hover:bg-transparent disabled:opacity-100':
                                                isCurrentPlan
                                        }
                                    )}
                                >
                                    {matchesCurrentBillingCycle
                                        ? 'Current Plan'
                                        : loadingPlan === plan.id
                                          ? 'Redirecting...'
                                          : 'Upgrade Plan'}
                                </Button>
                            )}
                        </article>
                    )
                })}
            </div>
        </section>
    )
}
