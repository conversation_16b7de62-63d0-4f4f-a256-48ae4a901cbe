import { useTheme } from 'next-themes'
import { useNavigate } from 'react-router'

import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar'
import { Icon } from './ui/icon'
import ButtonIcon from './button-icon'
import { WebSocketConnectionState } from '@/typings/agent'
import { useAppSelector } from '@/state/store'
import { selectUser } from '@/state/slice/user'
import { getFirstCharacters } from '@/lib/utils'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger
} from './ui/dropdown-menu'
import { useAuth } from '@/contexts/auth-context'

const RightSidebar = () => {
    const { theme, setTheme } = useTheme()
    const navigate = useNavigate()
    const wsConnectionState = useAppSelector(
        (state) => state.agent.wsConnectionState
    )
    const user = useAppSelector(selectUser)
    const { logout } = useAuth()

    const toggleTheme = () => {
        setTheme(theme === 'dark' ? 'light' : 'dark')
    }

    const handleLogout = () => {
        logout()
        navigate('/login')
    }

    const handleGetHelp = () => {
        window.open('https://discord.com/invite/intelligentinternet', '_blank')
    }

    if (!user) return null

    return (
        <div className="flex items-center justify-between flex-col h-full py-8 px-6 border-l border-neutral-200 dark:border-sidebar-border">
            <div className="flex flex-col items-center gap-4">
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Avatar className="size-10 cursor-pointer hover:opacity-80 transition-opacity">
                            <AvatarImage src={user?.avatar} />
                            <AvatarFallback>
                                {user?.first_name
                                    ? getFirstCharacters(
                                          `${user?.first_name} ${user?.last_name}`
                                      )
                                    : `II`}
                            </AvatarFallback>
                        </Avatar>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent
                        align="end"
                        className="w-[189px] space-y-4"
                    >
                        <DropdownMenuItem className="flex items-center gap-2 p-0">
                            <Avatar className="size-10">
                                <AvatarImage src={user?.avatar} />
                                <AvatarFallback className="text-xs">
                                    {user?.first_name
                                        ? getFirstCharacters(
                                              `${user?.first_name} ${user?.last_name}`
                                          )
                                        : `II`}
                                </AvatarFallback>
                            </Avatar>
                            <div className="flex flex-col min-w-0">
                                <span className="font-semibold text-sm">
                                    {user?.first_name} {user?.last_name}
                                </span>
                                <span className="text-xs truncate">
                                    {user?.email}
                                </span>
                            </div>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                            className="flex items-center gap-[6px] p-0"
                            onClick={() => navigate('/settings/account')}
                        >
                            <Icon name="user-2" className="size-4 fill-black" />
                            <span>Account</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                            className="flex items-center gap-[6px] p-0"
                            onClick={() => navigate('/settings/subscription')}
                        >
                            <Icon
                                name="dollar-circle"
                                className="size-4 fill-black"
                            />
                            <span>Subscription</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                            className="flex items-center gap-[6px] p-0"
                            onClick={() => navigate('/settings/account')}
                        >
                            <Icon
                                name="receipt"
                                className="size-4 fill-black"
                            />
                            <span>Payment &amp; Billing</span>
                        </DropdownMenuItem>
                        <DropdownMenuSeparator className="my-3" />
                        <DropdownMenuItem
                            className="flex items-center gap-[6px] p-0"
                            onClick={() => navigate('/settings/general')}
                        >
                            <Icon
                                name="setting-2"
                                className="size-4 fill-black"
                            />
                            <span>Settings</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                            className="flex items-center justify-between gap-[6px] p-0"
                            onClick={handleGetHelp}
                        >
                            <div className="flex items-center gap-[6px]">
                                <Icon
                                    name="help"
                                    className="size-4 stroke-black"
                                />
                                <span>Get Help</span>
                            </div>
                            <Icon
                                name="arrow-right-2"
                                className="size-4 fill-black"
                            />
                        </DropdownMenuItem>
                        <DropdownMenuSeparator className="my-3" />
                        <DropdownMenuItem
                            className="flex items-center gap-[6px] text-red-2 p-0"
                            variant="destructive"
                            onClick={handleLogout}
                        >
                            <Icon name="logout" className="size-4 fill-red-2" />
                            <span>Sign out</span>
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>

                <ButtonIcon
                    name={theme === 'dark' ? 'sun' : 'moon'}
                    iconClassName="!fill-none"
                    className="border border-black"
                    onClick={toggleTheme}
                />
            </div>
            {wsConnectionState === WebSocketConnectionState.CONNECTED && (
                <Icon name="connected" />
            )}
        </div>
    )
}

export default RightSidebar
