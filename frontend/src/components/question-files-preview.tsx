import { Folder, Loader2 } from 'lucide-react'
import { Icon } from './ui/icon'
import { getFileIconAndColor } from '@/utils/file-utils'
import type { FileUploadStatus } from '@/hooks/use-upload-files'

interface FilesPreviewProps {
  files: FileUploadStatus[]
  isUploading: boolean
  onRemove: (fileName: string) => void
}

const FilesPreview = ({ files, isUploading, onRemove }: FilesPreviewProps) => {
  if (files.length === 0) return null

  return (
    <div className="absolute top-4 left-4 right-2 flex items-center overflow-auto gap-2 z-10">
      {files.map((file) => {
        if (file.isImage && file.preview) {
          return (
            <div key={file.name} className="relative">
              <div className="size-12 rounded-lg overflow-hidden">
                <img src={file.preview} alt={file.name} className="w-full h-full object-cover" />
              </div>
              {(isUploading || file.loading) && (
                <div className="absolute inset-0 flex items-center justify-center bg-black/30 rounded-xl">
                  <Loader2 className="size-5 text-black animate-spin" />
                </div>
              )}
              <button onClick={() => onRemove(file.name)} className="absolute right-1 top-1 cursor-pointer">
                <Icon name="close-circle" className="size-4" />
              </button>
            </div>
          )
        }

        if (file.isFolder) {
          return (
            <div key={file.name} className="flex items-center gap-2 bg-neutral-900 text-white rounded-full px-3 py-2 border border-gray-700 shadow-sm">
              <div className="flex items-center justify-center w-10 h-10 bg-blue-600 rounded-full">
                {isUploading || file.loading ? (
                  <Loader2 className="size-5 text-white animate-spin" />
                ) : (
                  <Folder className="size-5 text-white" />
                )}
              </div>
              <div className="flex flex-col">
                <span className="text-sm font-medium truncate max-w-[200px]">{file.name}</span>
                <span className="text-xs text-gray-500">
                  {file.fileCount ? `${file.fileCount} ${file.fileCount === 1 ? 'file' : 'files'}` : 'Folder'}
                </span>
              </div>
            </div>
          )
        }

        const { label } = getFileIconAndColor(file.name)
        return (
          <div key={file.name} className="relative flex items-center gap-2 dark:bg-grey text-white rounded-lg p-2 pr-7">
            {(isUploading || file.loading) && (
              <div className={`flex items-center justify-center w-10 h-10 rounded-full`}>
                <Loader2 className="size-5 text-black animate-spin" />
              </div>
            )}
            <div className="flex flex-col text-black">
              <span className="text-xs font-semibold truncate max-w-[145px]">{file.name}</span>
              <span className="text-xs">{label}</span>
            </div>
            <button onClick={() => onRemove(file.name)} className="absolute right-1 top-1 cursor-pointer">
              <Icon name="close-circle" className="size-4" />
            </button>
          </div>
        )
      })}
    </div>
  )
}

export default FilesPreview

